import { IMovie } from '@/models/Movie';
import { ISeries } from '@/models/Series';
import { IEpisode } from '@/models/Episode';

export interface SchemaMarkup {
  '@context': string;
  '@type': string;
  [key: string]: any;
}

export class SchemaGenerator {
  private static baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://streamzen.com';
  private static siteName = 'StreamZen';

  static generateWebsiteSchema(): SchemaMarkup {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: this.siteName,
      url: this.baseUrl,
      description: 'Premium streaming platform with the latest movies, TV series, and episodes. Watch HD content with multiple streaming sources.',
      potentialAction: {
        '@type': 'SearchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${this.baseUrl}/search?q={search_term_string}`,
        },
        'query-input': 'required name=search_term_string',
      },
      publisher: {
        '@type': 'Organization',
        name: this.siteName,
        url: this.baseUrl,
        logo: {
          '@type': 'ImageObject',
          url: `${this.baseUrl}/logo.png`,
        },
      },
    };
  }

  static generateMovieSchema(movie: IMovie): SchemaMarkup {
    const schema: SchemaMarkup = {
      '@context': 'https://schema.org',
      '@type': 'Movie',
      name: movie.title,
      url: `${this.baseUrl}/watch/movie/${movie.imdbId}`,
      description: movie.description,
      image: movie.posterUrl,
      datePublished: movie.year?.toString(),
      genre: movie.genres,
      duration: movie.runtime,
      contentRating: movie.rating,
      aggregateRating: movie.imdbRating ? {
        '@type': 'AggregateRating',
        ratingValue: movie.imdbRating,
        ratingCount: movie.imdbVotes?.replace(/,/g, '') || '1000',
        bestRating: '10',
        worstRating: '1',
      } : undefined,
      director: movie.director ? {
        '@type': 'Person',
        name: movie.director,
      } : undefined,
      actor: movie.cast?.slice(0, 5).map(actor => ({
        '@type': 'Person',
        name: actor,
      })),
      productionCompany: {
        '@type': 'Organization',
        name: movie.country || 'Unknown',
      },
      inLanguage: movie.language,
      keywords: [
        movie.title,
        `${movie.title} ${movie.year}`,
        'watch online',
        'free movie',
        'HD movie',
        ...(movie.genres || []),
      ].join(', '),
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
        availability: 'https://schema.org/InStock',
        url: `${this.baseUrl}/watch/movie/${movie.imdbId}`,
      },
      potentialAction: {
        '@type': 'WatchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${this.baseUrl}/watch/movie/${movie.imdbId}`,
          actionPlatform: [
            'https://schema.org/DesktopWebPlatform',
            'https://schema.org/MobileWebPlatform',
          ],
        },
        expectsAcceptanceOf: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'USD',
          eligibleRegion: {
            '@type': 'Country',
            name: 'US',
          },
        },
      },
    };

    // Remove undefined values
    return JSON.parse(JSON.stringify(schema));
  }

  static generateSeriesSchema(series: ISeries): SchemaMarkup {
    const schema: SchemaMarkup = {
      '@context': 'https://schema.org',
      '@type': 'TVSeries',
      name: series.title,
      url: `${this.baseUrl}/watch/series/${series.imdbId}`,
      description: series.description,
      image: series.posterUrl,
      startDate: series.startYear?.toString(),
      endDate: series.endYear?.toString(),
      numberOfSeasons: series.totalSeasons,
      numberOfEpisodes: series.totalEpisodes,
      genre: series.genres,
      contentRating: series.rating,
      aggregateRating: series.imdbRating ? {
        '@type': 'AggregateRating',
        ratingValue: series.imdbRating,
        ratingCount: series.imdbVotes?.replace(/,/g, '') || '1000',
        bestRating: '10',
        worstRating: '1',
      } : undefined,
      actor: series.cast?.slice(0, 5).map(actor => ({
        '@type': 'Person',
        name: actor,
      })),
      productionCompany: {
        '@type': 'Organization',
        name: series.country || 'Unknown',
      },
      inLanguage: series.language,
      keywords: [
        series.title,
        `${series.title} ${series.startYear}`,
        'watch online',
        'free series',
        'HD series',
        'TV show',
        ...(series.genres || []),
      ].join(', '),
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
        availability: 'https://schema.org/InStock',
        url: `${this.baseUrl}/watch/series/${series.imdbId}`,
      },
      potentialAction: {
        '@type': 'WatchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${this.baseUrl}/watch/series/${series.imdbId}`,
          actionPlatform: [
            'https://schema.org/DesktopWebPlatform',
            'https://schema.org/MobileWebPlatform',
          ],
        },
        expectsAcceptanceOf: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'USD',
          eligibleRegion: {
            '@type': 'Country',
            name: 'US',
          },
        },
      },
    };

    return JSON.parse(JSON.stringify(schema));
  }

  static generateEpisodeSchema(episode: IEpisode, series?: ISeries): SchemaMarkup {
    const episodeTitle = episode.episodeTitle || `Episode ${episode.episode}`;
    
    const schema: SchemaMarkup = {
      '@context': 'https://schema.org',
      '@type': 'TVEpisode',
      name: episodeTitle,
      episodeNumber: episode.episode,
      seasonNumber: episode.season,
      url: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,
      description: episode.description || `${episode.seriesTitle} Season ${episode.season} Episode ${episode.episode}`,
      image: series?.posterUrl,
      datePublished: episode.airDate ? new Date(episode.airDate).toISOString() : undefined,
      duration: episode.runtime,
      partOfSeries: {
        '@type': 'TVSeries',
        name: episode.seriesTitle,
        url: `${this.baseUrl}/watch/series/${episode.imdbId}`,
      },
      partOfSeason: {
        '@type': 'TVSeason',
        seasonNumber: episode.season,
        partOfSeries: {
          '@type': 'TVSeries',
          name: episode.seriesTitle,
        },
      },
      aggregateRating: episode.imdbRating ? {
        '@type': 'AggregateRating',
        ratingValue: episode.imdbRating,
        bestRating: '10',
        worstRating: '1',
      } : undefined,
      genre: episode.genres || series?.genres,
      inLanguage: series?.language,
      keywords: [
        episode.seriesTitle,
        episodeTitle,
        `S${episode.season}E${episode.episode}`,
        'watch online',
        'free episode',
        'HD episode',
        ...(episode.genres || series?.genres || []),
      ].join(', '),
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
        availability: 'https://schema.org/InStock',
        url: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,
      },
      potentialAction: {
        '@type': 'WatchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,
          actionPlatform: [
            'https://schema.org/DesktopWebPlatform',
            'https://schema.org/MobileWebPlatform',
          ],
        },
      },
    };

    return JSON.parse(JSON.stringify(schema));
  }

  static generateBreadcrumbSchema(items: Array<{ name: string; url: string }>): SchemaMarkup {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: items.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: `${this.baseUrl}${item.url}`,
      })),
    };
  }

  static generateCollectionPageSchema(
    name: string,
    description: string,
    url: string,
    items: Array<{ name: string; url: string; image?: string }>
  ): SchemaMarkup {
    return {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      name,
      description,
      url: `${this.baseUrl}${url}`,
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: items.length,
        itemListElement: items.map((item, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          url: `${this.baseUrl}${item.url}`,
          name: item.name,
          ...(item.image && { image: item.image }),
        })),
      },
    };
  }
}
