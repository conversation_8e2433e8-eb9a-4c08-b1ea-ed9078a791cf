/**
 * SEO Monitoring and Analytics Utilities
 * Tracks SEO performance and provides insights for optimization
 */

export interface SEOMetrics {
  pageViews: number;
  uniqueVisitors: number;
  bounceRate: number;
  avgSessionDuration: number;
  organicTraffic: number;
  searchImpressions: number;
  searchClicks: number;
  avgPosition: number;
  ctr: number;
}

export interface PageSEOData {
  url: string;
  title: string;
  metaDescription: string;
  keywords: string[];
  lastModified: Date;
  indexStatus: 'indexed' | 'not_indexed' | 'pending';
  canonicalUrl: string;
  structuredData: boolean;
  openGraph: boolean;
  twitterCards: boolean;
}

export class SEOMonitor {
  private static instance: SEOMonitor;
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://streamzen.com';
  }

  static getInstance(): SEOMonitor {
    if (!SEOMonitor.instance) {
      SEOMonitor.instance = new SEOMonitor();
    }
    return SEOMonitor.instance;
  }

  /**
   * Generate comprehensive SEO report for a page
   */
  async generatePageSEOReport(url: string): Promise<PageSEOData> {
    const response = await fetch(url);
    const html = await response.text();
    
    // Parse HTML to extract SEO data
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    const metaDescMatch = html.match(/<meta[^>]*name="description"[^>]*content="([^"]*)"[^>]*>/i);
    const canonicalMatch = html.match(/<link[^>]*rel="canonical"[^>]*href="([^"]*)"[^>]*>/i);
    
    // Check for structured data
    const hasStructuredData = html.includes('application/ld+json');
    const hasOpenGraph = html.includes('property="og:');
    const hasTwitterCards = html.includes('name="twitter:');
    
    // Extract keywords from meta tags
    const keywordsMatch = html.match(/<meta[^>]*name="keywords"[^>]*content="([^"]*)"[^>]*>/i);
    const keywords = keywordsMatch ? keywordsMatch[1].split(',').map(k => k.trim()) : [];

    return {
      url,
      title: titleMatch ? titleMatch[1] : '',
      metaDescription: metaDescMatch ? metaDescMatch[1] : '',
      keywords,
      lastModified: new Date(),
      indexStatus: 'pending', // Would need to check with search engines
      canonicalUrl: canonicalMatch ? canonicalMatch[1] : url,
      structuredData: hasStructuredData,
      openGraph: hasOpenGraph,
      twitterCards: hasTwitterCards
    };
  }

  /**
   * Validate sitemap structure and URLs
   */
  async validateSitemap(): Promise<{
    totalUrls: number;
    validUrls: number;
    errors: string[];
    lastModified: Date;
  }> {
    try {
      const sitemapResponse = await fetch(`${this.baseUrl}/sitemap.xml`);
      const sitemapXml = await sitemapResponse.text();
      
      // Parse sitemap XML to count URLs
      const urlMatches = sitemapXml.match(/<loc>([^<]+)<\/loc>/g) || [];
      const totalUrls = urlMatches.length;
      
      // Validate a sample of URLs
      const sampleUrls = urlMatches.slice(0, 10).map(match => 
        match.replace(/<\/?loc>/g, '')
      );
      
      let validUrls = 0;
      const errors: string[] = [];
      
      for (const url of sampleUrls) {
        try {
          const response = await fetch(url, { method: 'HEAD' });
          if (response.ok) {
            validUrls++;
          } else {
            errors.push(`${url}: HTTP ${response.status}`);
          }
        } catch (error) {
          errors.push(`${url}: ${error.message}`);
        }
      }
      
      return {
        totalUrls,
        validUrls: Math.round((validUrls / sampleUrls.length) * totalUrls),
        errors,
        lastModified: new Date()
      };
    } catch (error) {
      return {
        totalUrls: 0,
        validUrls: 0,
        errors: [`Sitemap validation failed: ${error.message}`],
        lastModified: new Date()
      };
    }
  }

  /**
   * Check robots.txt configuration
   */
  async validateRobotsTxt(): Promise<{
    exists: boolean;
    allowsCrawling: boolean;
    hasSitemap: boolean;
    content: string;
    errors: string[];
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/robots.txt`);
      const content = await response.text();
      
      const allowsCrawling = !content.includes('Disallow: /');
      const hasSitemap = content.includes('Sitemap:');
      
      const errors: string[] = [];
      if (!hasSitemap) {
        errors.push('No sitemap reference found in robots.txt');
      }
      if (content.includes('Disallow: /')) {
        errors.push('Robots.txt may be blocking important content');
      }
      
      return {
        exists: response.ok,
        allowsCrawling,
        hasSitemap,
        content,
        errors
      };
    } catch (error) {
      return {
        exists: false,
        allowsCrawling: false,
        hasSitemap: false,
        content: '',
        errors: [`Robots.txt check failed: ${error.message}`]
      };
    }
  }

  /**
   * Generate SEO recommendations based on content analysis
   */
  generateSEORecommendations(pageData: PageSEOData): string[] {
    const recommendations: string[] = [];
    
    // Title optimization
    if (!pageData.title) {
      recommendations.push('Add a title tag to the page');
    } else if (pageData.title.length < 30) {
      recommendations.push('Title is too short - aim for 50-60 characters');
    } else if (pageData.title.length > 60) {
      recommendations.push('Title is too long - keep it under 60 characters');
    }
    
    // Meta description optimization
    if (!pageData.metaDescription) {
      recommendations.push('Add a meta description to the page');
    } else if (pageData.metaDescription.length < 120) {
      recommendations.push('Meta description is too short - aim for 150-160 characters');
    } else if (pageData.metaDescription.length > 160) {
      recommendations.push('Meta description is too long - keep it under 160 characters');
    }
    
    // Keywords optimization
    if (pageData.keywords.length === 0) {
      recommendations.push('Add relevant keywords to improve search visibility');
    } else if (pageData.keywords.length > 10) {
      recommendations.push('Too many keywords - focus on 5-10 most relevant ones');
    }
    
    // Technical SEO
    if (!pageData.structuredData) {
      recommendations.push('Add structured data (Schema.org) for better search results');
    }
    if (!pageData.openGraph) {
      recommendations.push('Add Open Graph tags for better social media sharing');
    }
    if (!pageData.twitterCards) {
      recommendations.push('Add Twitter Card tags for enhanced Twitter sharing');
    }
    
    // URL optimization
    if (pageData.url !== pageData.canonicalUrl) {
      recommendations.push('Ensure canonical URL is properly set');
    }
    
    return recommendations;
  }

  /**
   * Track page performance metrics
   */
  async trackPageMetrics(url: string): Promise<{
    loadTime: number;
    firstContentfulPaint: number;
    largestContentfulPaint: number;
    cumulativeLayoutShift: number;
    firstInputDelay: number;
  }> {
    // This would integrate with real performance monitoring tools
    // For now, return mock data structure
    return {
      loadTime: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      cumulativeLayoutShift: 0,
      firstInputDelay: 0
    };
  }

  /**
   * Generate comprehensive SEO audit report
   */
  async generateSEOAudit(): Promise<{
    score: number;
    issues: string[];
    recommendations: string[];
    technicalSEO: any;
    contentSEO: any;
    performanceSEO: any;
  }> {
    const sitemapValidation = await this.validateSitemap();
    const robotsValidation = await this.validateRobotsTxt();
    
    const issues: string[] = [
      ...sitemapValidation.errors,
      ...robotsValidation.errors
    ];
    
    const recommendations: string[] = [];
    let score = 100;
    
    // Deduct points for issues
    score -= issues.length * 10;
    
    // Add recommendations based on findings
    if (!robotsValidation.hasSitemap) {
      recommendations.push('Add sitemap reference to robots.txt');
    }
    if (sitemapValidation.totalUrls === 0) {
      recommendations.push('Generate and submit XML sitemaps');
    }
    
    return {
      score: Math.max(0, score),
      issues,
      recommendations,
      technicalSEO: {
        sitemap: sitemapValidation,
        robots: robotsValidation
      },
      contentSEO: {
        // Would include content analysis results
      },
      performanceSEO: {
        // Would include performance metrics
      }
    };
  }
}
