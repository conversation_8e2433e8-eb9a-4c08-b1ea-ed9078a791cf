import { Metadata } from 'next';
import { IMovie } from '@/models/Movie';
import { ISeries } from '@/models/Series';
import { IEpisode } from '@/models/Episode';

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  canonical?: string;
  ogImage?: string;
  ogType?: 'website' | 'video.movie' | 'video.tv_show' | 'video.episode';
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  section?: string;
  tags?: string[];
}

export class SEOGenerator {
  private static baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://streamzen.com';
  private static siteName = 'StreamZen';
  private static defaultDescription = 'Premium streaming platform with the latest movies, TV series, and episodes. Watch HD content with multiple streaming sources.';

  static generateMetadata(config: SEOConfig): Metadata {
    const {
      title,
      description,
      keywords = [],
      canonical,
      ogImage,
      ogType = 'website',
      publishedTime,
      modifiedTime,
      authors = [],
      section,
      tags = []
    } = config;

    const fullTitle = title.includes(this.siteName) ? title : `${title} | ${this.siteName}`;
    const url = canonical ? `${this.baseUrl}${canonical}` : this.baseUrl;
    const defaultImage = `${this.baseUrl}/og-default.jpg`;

    return {
      title: fullTitle,
      description,
      keywords: keywords.join(', '),
      authors: authors.map(name => ({ name })),
      creator: this.siteName,
      publisher: this.siteName,
      formatDetection: {
        email: false,
        address: false,
        telephone: false,
      },
      metadataBase: new URL(this.baseUrl),
      alternates: {
        canonical: url,
      },
      openGraph: {
        title: fullTitle,
        description,
        url,
        siteName: this.siteName,
        images: [
          {
            url: ogImage || defaultImage,
            width: 1200,
            height: 630,
            alt: title,
          },
        ],
        locale: 'en_US',
        type: ogType,
        ...(publishedTime && { publishedTime }),
        ...(modifiedTime && { modifiedTime }),
        ...(section && { section }),
        ...(tags.length > 0 && { tags }),
      },
      twitter: {
        card: 'summary_large_image',
        title: fullTitle,
        description,
        images: [ogImage || defaultImage],
        creator: '@streamzen',
        site: '@streamzen',
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      verification: {
        google: process.env.GOOGLE_VERIFICATION_ID,
        yandex: process.env.YANDEX_VERIFICATION_ID,
        yahoo: process.env.YAHOO_VERIFICATION_ID,
      },
    };
  }

  static generateMovieMetadata(movie: IMovie): Metadata {
    const title = `Watch ${movie.title} (${movie.year}) Online Free`;
    const description = `Watch ${movie.title} (${movie.year}) online free in HD quality. ${movie.description || `Starring ${movie.cast?.slice(0, 3).join(', ') || 'top actors'}. Stream now on ${this.siteName}.`}`;
    
    const keywords = [
      movie.title,
      `${movie.title} ${movie.year}`,
      `watch ${movie.title}`,
      `${movie.title} online`,
      `${movie.title} free`,
      'watch movies online',
      'free movies',
      'HD movies',
      ...(movie.genres || []),
      ...(movie.cast?.slice(0, 5) || []),
      movie.director,
      movie.language,
      movie.country,
    ].filter(Boolean);

    return this.generateMetadata({
      title,
      description,
      keywords,
      canonical: `/watch/movie/${movie.imdbId}`,
      ogImage: movie.posterUrl,
      ogType: 'video.movie',
      publishedTime: movie.createdAt ? new Date(movie.createdAt).toISOString() : undefined,
      modifiedTime: movie.updatedAt ? new Date(movie.updatedAt).toISOString() : undefined,
      authors: [movie.director].filter(Boolean),
      section: 'Movies',
      tags: movie.genres,
    });
  }

  static generateSeriesMetadata(series: ISeries): Metadata {
    const title = `Watch ${series.title} (${series.startYear}${series.endYear ? `-${series.endYear}` : ''}) Online Free`;
    const description = `Watch ${series.title} TV series online free in HD quality. ${series.description || `${series.totalSeasons} seasons available. Starring ${series.cast?.slice(0, 3).join(', ') || 'top actors'}. Stream all episodes now on ${this.siteName}.`}`;
    
    const keywords = [
      series.title,
      `${series.title} ${series.startYear}`,
      `watch ${series.title}`,
      `${series.title} online`,
      `${series.title} free`,
      `${series.title} episodes`,
      'watch series online',
      'free TV shows',
      'HD series',
      ...(series.genres || []),
      ...(series.cast?.slice(0, 5) || []),
      series.language,
      series.country,
    ].filter(Boolean);

    return this.generateMetadata({
      title,
      description,
      keywords,
      canonical: `/watch/series/${series.imdbId}`,
      ogImage: series.posterUrl,
      ogType: 'video.tv_show',
      publishedTime: series.createdAt ? new Date(series.createdAt).toISOString() : undefined,
      modifiedTime: series.updatedAt ? new Date(series.updatedAt).toISOString() : undefined,
      section: 'TV Series',
      tags: series.genres,
    });
  }

  static generateEpisodeMetadata(episode: IEpisode, series?: ISeries): Metadata {
    const episodeTitle = episode.episodeTitle || `Episode ${episode.episode}`;
    const title = `Watch ${episode.seriesTitle} S${episode.season}E${episode.episode} - ${episodeTitle} Online Free`;
    const description = `Watch ${episode.seriesTitle} Season ${episode.season} Episode ${episode.episode} "${episodeTitle}" online free in HD quality. ${episode.description || `Latest episode from ${episode.seriesTitle}. Stream now on ${this.siteName}.`}`;
    
    const keywords = [
      episode.seriesTitle,
      `${episode.seriesTitle} S${episode.season}E${episode.episode}`,
      `${episode.seriesTitle} season ${episode.season}`,
      `watch ${episode.seriesTitle}`,
      episodeTitle,
      'watch episodes online',
      'free episodes',
      'HD episodes',
      ...(episode.genres || series?.genres || []),
    ].filter(Boolean);

    return this.generateMetadata({
      title,
      description,
      keywords,
      canonical: `/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,
      ogImage: series?.posterUrl,
      ogType: 'video.episode',
      publishedTime: episode.createdAt ? new Date(episode.createdAt).toISOString() : undefined,
      modifiedTime: episode.updatedAt ? new Date(episode.updatedAt).toISOString() : undefined,
      section: 'Episodes',
      tags: episode.genres || series?.genres,
    });
  }

  static generatePageMetadata(
    title: string,
    description: string,
    path: string,
    additionalKeywords: string[] = []
  ): Metadata {
    const keywords = [
      'watch movies online',
      'free movies',
      'HD movies',
      'TV series online',
      'free episodes',
      'streaming platform',
      this.siteName,
      ...additionalKeywords,
    ];

    return this.generateMetadata({
      title,
      description,
      keywords,
      canonical: path,
    });
  }
}
