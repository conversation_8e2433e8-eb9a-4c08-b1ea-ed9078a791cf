const XLSX = require('xlsx');
const path = require('path');
const mongoose = require('mongoose');
const axios = require('axios');
const cheerio = require('cheerio');

// Simple parallel scraper without worker threads for better compatibility
class SimpleParallelScraper {
  constructor() {
    this.PARALLEL_LIMIT = 50; // 50 concurrent requests
    this.BATCH_SIZE = 100; // Process 100 movies per batch
    this.DELAY_BETWEEN_REQUESTS = 1000; // 1 second delay
    this.processedCount = 0;
    this.successCount = 0;
    this.errorCount = 0;
    this.startTime = Date.now();
    this.results = {
      movies: [],
      series: [],
      errors: []
    };
  }

  async initialize() {
    console.log('🚀 Initializing Simple Parallel Scraper...');
    
    // Connect to MongoDB
    if (!mongoose.connection.readyState) {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';
      await mongoose.connect(mongoUri);
      console.log('✅ Connected to MongoDB');
    }

    // Define schemas inline
    const movieSchema = new mongoose.Schema({
      imdbId: { type: String, required: true, unique: true },
      title: { type: String, required: true },
      year: { type: Number, required: true },
      description: String,
      genres: [String],
      cast: [String],
      director: String,
      language: String,
      country: String,
      imdbRating: Number,
      imdbVotes: String,
      posterUrl: String,
      embedUrl: String,
      vidsrcUrl: String,
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });

    const seriesSchema = new mongoose.Schema({
      imdbId: { type: String, required: true, unique: true },
      title: { type: String, required: true },
      startYear: { type: Number, required: true },
      endYear: Number,
      description: String,
      genres: [String],
      cast: [String],
      creator: String,
      language: String,
      country: String,
      imdbRating: Number,
      imdbVotes: String,
      posterUrl: String,
      embedUrl: String,
      vidsrcUrl: String,
      totalSeasons: Number,
      status: String,
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });

    // Create models
    this.Movie = mongoose.models.Movie || mongoose.model('Movie', movieSchema);
    this.Series = mongoose.models.Series || mongoose.model('Series', seriesSchema);
  }

  async loadExcelFile() {
    console.log('📊 Loading Excel file...');
    
    const excelPath = path.join(__dirname, 'imdb_movie_ids_final.xlsx');
    const workbook = XLSX.readFile(excelPath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);
    
    const imdbIds = data.map(row => row.TitleID).filter(id => id && id.startsWith('tt'));
    
    console.log(`✅ Loaded ${imdbIds.length} IMDb IDs from Excel file`);
    return imdbIds;
  }

  getRandomUserAgent() {
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ];
    return userAgents[Math.floor(Math.random() * userAgents.length)];
  }

  async fetchPage(imdbId) {
    const url = `https://www.imdb.com/title/${imdbId}/`;
    const headers = {
      'User-Agent': this.getRandomUserAgent(),
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Cache-Control': 'max-age=0',
    };

    const response = await axios.get(url, { headers, timeout: 30000 });
    return cheerio.load(response.data);
  }

  extractBasicInfo($) {
    const titleElement = $('h1[data-testid="hero__pageTitle"] span[data-testid="hero__primary-text"]');
    const title = titleElement.text().trim();
    
    if (!title) {
      throw new Error('Could not extract title from IMDb page');
    }

    const yearElement = $('ul.ipc-inline-list a[href*="/releaseinfo/"]');
    const yearText = yearElement.text().trim();
    const year = parseInt(yearText) || new Date().getFullYear();

    const typeIndicators = $('ul.ipc-inline-list li').text().toLowerCase();
    const isMovie = !typeIndicators.includes('tv series') && !typeIndicators.includes('tv mini series');
    
    return { title, year, type: isMovie ? 'movie' : 'series' };
  }

  extractGenres($) {
    const genres = [];
    const genreSelectors = [
      '[data-testid="genres"] .ipc-chip .ipc-chip__text',
      '[data-testid="genres"] .ipc-chip__text',
      'li[data-testid="storyline-genres"] .ipc-metadata-list-item__list-content-item'
    ];

    for (const selector of genreSelectors) {
      const elements = $(selector);
      if (elements.length > 0) {
        elements.each((_, element) => {
          const genre = $(element).text().trim();
          if (genre && genre.length > 0 && genre.length < 50 && !genres.includes(genre)) {
            const skipTexts = ['Genres', 'Genre', 'See all', 'More', 'All', '...'];
            if (!skipTexts.includes(genre)) {
              genres.push(genre);
            }
          }
        });
        if (genres.length > 0) break;
      }
    }
    return genres;
  }

  extractCast($) {
    const cast = [];
    const castSelectors = [
      'section[data-testid="title-cast"] a[data-testid="title-cast-item__actor"]',
      '.cast_list .primary_photo + td a'
    ];

    for (const selector of castSelectors) {
      const elements = $(selector);
      if (elements.length > 0) {
        elements.each((_, element) => {
          const actorName = $(element).text().trim();
          if (actorName && !cast.includes(actorName) && cast.length < 10) {
            cast.push(actorName);
          }
        });
        break;
      }
    }
    return cast;
  }

  extractIMDbRating($) {
    const ratingElement = $('div[data-testid="hero-rating-bar__aggregate-rating__score"] span');
    const rating = parseFloat(ratingElement.text().trim()) || undefined;
    
    const votesElement = $('div.sc-d541859f-3');
    const votes = votesElement.text().trim() || undefined;
    
    return { rating, votes };
  }

  extractDescription($) {
    const selectors = [
      'span[data-testid="plot-xl"]',
      'span[data-testid="plot-l"]',
      'span[data-testid="plot"]'
    ];

    for (const selector of selectors) {
      const element = $(selector);
      const text = element.text().trim();
      if (text && text.length > 10) return text;
    }
    return undefined;
  }

  extractRating($) {
    const ratingElement = $('ul.ipc-inline-list a[href*="/parentalguide/"]');
    return ratingElement.text().trim() || undefined;
  }

  extractRuntime($) {
    const runtimeElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < runtimeElements.length; i++) {
      const text = $(runtimeElements[i]).text().trim();
      if (text.includes('h') || text.includes('min')) {
        return text;
      }
    }
    return undefined;
  }

  extractPopularity($) {
    const popularityElement = $('div[data-testid="hero-rating-bar__popularity__score"]');
    const popularity = parseInt(popularityElement.text().trim()) || undefined;

    const deltaElement = $('div[data-testid="hero-rating-bar__popularity__delta"]');
    const deltaText = deltaElement.text().trim();
    const delta = deltaText ? parseInt(deltaText.replace(/[^\d-]/g, '')) : undefined;

    return { popularity, delta };
  }

  extractPosterUrl($) {
    const selectors = [
      'div[data-testid="hero-media__poster"] img',
      '.ipc-image[data-testid="hero-media__poster"]',
      '.poster img',
      '.ipc-media img',
      'img[class*="poster"]',
      'a[class*="ipc-lockup-overlay"] img'
    ];

    for (const selector of selectors) {
      const element = $(selector);
      const src = element.attr('src');
      if (src && src.includes('media-amazon.com')) {
        return src.replace(/\._.*?_\./, '._V1_FMjpg_UX1000_.').replace(/\._.*?\./, '._V1_FMjpg_UX1000_.');
      }
    }
    return undefined;
  }

  extractBackdropUrl($) {
    const selectors = [
      '.hero-media__slate-overlay img',
      '.slate img',
      '.hero__background img',
      'div[data-testid="hero-media"] img'
    ];

    for (const selector of selectors) {
      const element = $(selector);
      const src = element.attr('src');
      if (src && src.includes('media-amazon.com')) {
        return src.replace(/\._.*?_\./, '._V1_FMjpg_UX1920_.');
      }
    }
    return undefined;
  }

  extractTrailerInfo($) {
    const trailerElement = $('a[data-testid="video-player-slate-overlay"]');
    const url = trailerElement.attr('href') || undefined;

    const runtimeElement = $('span[data-testid="video-player-slate-runtime"]');
    const runtime = runtimeElement.text().trim() || undefined;

    const likesElement = $('span.ipc-reaction-summary__label');
    const likes = likesElement.text().trim() || undefined;

    return { url, runtime, likes };
  }

  generateVidSrcUrl(imdbId, type) {
    if (type === 'movie') {
      return `https://vidsrc.me/embed/movie?imdb=${imdbId}`;
    } else {
      return `https://vidsrc.me/embed/tv?imdb=${imdbId}&season=1&episode=1`;
    }
  }

  async scrapeContent(imdbId) {
    try {
      const $ = await this.fetchPage(imdbId);
      const basicInfo = this.extractBasicInfo($);
      const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);
      const { popularity, delta: popularityDelta } = this.extractPopularity($);
      const { url: trailerUrl, runtime: trailerRuntime, likes: trailerLikes } = this.extractTrailerInfo($);

      const commonData = {
        imdbId,
        title: basicInfo.title,
        year: basicInfo.year,
        rating: this.extractRating($), // MPAA rating
        runtime: this.extractRuntime($),
        imdbRating,
        imdbVotes,
        popularity,
        popularityDelta,
        posterUrl: this.extractPosterUrl($),
        backdropUrl: this.extractBackdropUrl($),
        trailerUrl,
        trailerRuntime,
        trailerLikes,
        description: this.extractDescription($),
        genres: this.extractGenres($),
        cast: this.extractCast($),
        language: this.extractLanguage($),
        country: this.extractCountry($),
        embedUrl: this.generateVidSrcUrl(imdbId, basicInfo.type),
        vidsrcUrl: this.generateVidSrcUrl(imdbId, basicInfo.type),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Add type-specific fields
      if (basicInfo.type === 'movie') {
        commonData.director = this.extractDirector($);
        commonData.releaseDate = new Date(basicInfo.year, 0, 1); // Convert year to date
      } else {
        commonData.startYear = basicInfo.year;
        commonData.endYear = this.extractEndYear($);
        commonData.creator = this.extractCreator($);
        commonData.totalSeasons = this.extractTotalSeasons($);
        commonData.status = this.extractStatus($);
      }

      return {
        success: true,
        type: basicInfo.type,
        data: commonData
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        imdbId
      };
    }
  }

  extractDirector($) {
    const directorSelectors = [
      'li[data-testid="title-pc-principal-credit"]:contains("Director") .ipc-metadata-list-item__list-content-item',
      'li[data-testid="title-pc-principal-credit"]:contains("Directors") .ipc-metadata-list-item__list-content-item'
    ];

    for (const selector of directorSelectors) {
      const element = $(selector).first();
      const director = element.text().trim();
      if (director) return director;
    }
    return undefined;
  }

  extractCreator($) {
    const creatorSelectors = [
      'li[data-testid="title-pc-principal-credit"]:contains("Creator") .ipc-metadata-list-item__list-content-item',
      'li[data-testid="title-pc-principal-credit"]:contains("Creators") .ipc-metadata-list-item__list-content-item',
      '.credit_summary_item:contains("Creator") a',
      '.credit_summary_item:contains("Created by") a'
    ];

    for (const selector of creatorSelectors) {
      const element = $(selector).first();
      const creator = element.text().trim();
      if (creator) return creator;
    }
    return undefined;
  }

  extractEndYear($) {
    // Try to extract end year from series info
    const yearElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < yearElements.length; i++) {
      const text = $(yearElements[i]).text().trim();
      const yearMatch = text.match(/(\d{4})–(\d{4})/);
      if (yearMatch) {
        return parseInt(yearMatch[2]);
      }
    }
    return undefined;
  }

  extractTotalSeasons($) {
    // Try to extract total seasons from series info
    const seasonElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < seasonElements.length; i++) {
      const text = $(seasonElements[i]).text().trim();
      const seasonMatch = text.match(/(\d+)\s+seasons?/i);
      if (seasonMatch) {
        return parseInt(seasonMatch[1]);
      }
    }
    return 1; // Default to 1 season
  }

  extractStatus($) {
    // Try to determine if series is ongoing or ended
    const yearElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < yearElements.length; i++) {
      const text = $(yearElements[i]).text().trim();
      if (text.includes('–') && !text.match(/\d{4}–\d{4}/)) {
        return 'ongoing';
      } else if (text.match(/\d{4}–\d{4}/)) {
        return 'ended';
      }
    }
    return 'ongoing'; // Default to ongoing
  }

  async processBatch(imdbIds, batchIndex) {
    console.log(`🔄 Processing batch ${batchIndex}: ${imdbIds.length} items`);
    
    const promises = imdbIds.map(async (imdbId, index) => {
      // Add staggered delay
      await new Promise(resolve => setTimeout(resolve, index * 100));
      return this.scrapeContent(imdbId);
    });

    const results = await Promise.allSettled(promises);
    
    results.forEach((result, index) => {
      this.processedCount++;
      
      if (result.status === 'fulfilled' && result.value.success) {
        this.successCount++;
        if (result.value.type === 'movie') {
          this.results.movies.push(result.value.data);
        } else {
          this.results.series.push(result.value.data);
        }
      } else {
        this.errorCount++;
        this.results.errors.push({
          imdbId: imdbIds[index],
          error: result.status === 'rejected' ? result.reason.message : result.value.error
        });
      }
    });

    this.logProgress();
  }

  logProgress() {
    const elapsed = (Date.now() - this.startTime) / 1000;
    const rate = this.processedCount / elapsed;
    const eta = this.totalItems ? (this.totalItems - this.processedCount) / rate : 0;
    
    console.log(`📈 Progress: ${this.processedCount}/${this.totalItems || '?'} | ✅ ${this.successCount} | ❌ ${this.errorCount} | Rate: ${rate.toFixed(2)}/s | ETA: ${Math.round(eta/60)}min`);
  }

  async saveToDatabase() {
    console.log('💾 Saving results to database...');
    
    let savedMovies = 0;
    let savedSeries = 0;

    // Save movies
    for (const movieData of this.results.movies) {
      try {
        await this.Movie.findOneAndUpdate(
          { imdbId: movieData.imdbId },
          movieData,
          { upsert: true, new: true }
        );
        savedMovies++;
      } catch (error) {
        console.error(`❌ Error saving movie ${movieData.imdbId}:`, error.message);
      }
    }

    // Save series
    for (const seriesData of this.results.series) {
      try {
        await this.Series.findOneAndUpdate(
          { imdbId: seriesData.imdbId },
          seriesData,
          { upsert: true, new: true }
        );
        savedSeries++;
      } catch (error) {
        console.error(`❌ Error saving series ${seriesData.imdbId}:`, error.message);
      }
    }

    console.log(`✅ Saved to database: ${savedMovies} movies, ${savedSeries} series`);
  }

  async run() {
    try {
      await this.initialize();
      
      const imdbIds = await this.loadExcelFile();
      this.totalItems = imdbIds.length;
      
      console.log(`🚀 Starting parallel scraping of ${imdbIds.length} items...`);
      
      // Split into batches
      const batches = [];
      for (let i = 0; i < imdbIds.length; i += this.BATCH_SIZE) {
        batches.push(imdbIds.slice(i, i + this.BATCH_SIZE));
      }
      
      console.log(`📦 Created ${batches.length} batches of ${this.BATCH_SIZE} items each`);
      
      // Process batches
      for (let i = 0; i < batches.length; i++) {
        await this.processBatch(batches[i], i + 1);
        
        // Add delay between batches
        if (i < batches.length - 1) {
          console.log(`⏳ Waiting ${this.DELAY_BETWEEN_REQUESTS}ms before next batch...`);
          await new Promise(resolve => setTimeout(resolve, this.DELAY_BETWEEN_REQUESTS));
        }
      }
      
      console.log('🎉 All batches completed!');
      
      // Save results to database
      await this.saveToDatabase();
      
      // Generate final report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Scraper failed:', error);
    } finally {
      await mongoose.disconnect();
    }
  }

  generateReport() {
    const totalTime = (Date.now() - this.startTime) / 1000;
    const avgRate = this.processedCount / totalTime;
    
    console.log('\\n🎯 FINAL SCRAPING REPORT');
    console.log('========================');
    console.log(`📊 Total Processed: ${this.processedCount}`);
    console.log(`✅ Successful: ${this.successCount}`);
    console.log(`❌ Errors: ${this.errorCount}`);
    console.log(`🎬 Movies Found: ${this.results.movies.length}`);
    console.log(`📺 Series Found: ${this.results.series.length}`);
    console.log(`⏱️  Total Time: ${Math.round(totalTime/60)} minutes`);
    console.log(`⚡ Average Rate: ${avgRate.toFixed(2)} items/second`);
    console.log('========================\\n');
  }
}

// Run the scraper
async function main() {
  const scraper = new SimpleParallelScraper();
  await scraper.run();
  process.exit(0);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = SimpleParallelScraper;
