import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { apiClient } from '@/lib/api';
import VideoPlayer from '@/components/VideoPlayer';
import ContentInfo from '@/components/ContentInfo';
import VidSrcAPI from '@/lib/vidsrc';
import { SEOGenerator } from '@/lib/seo';
import { SchemaGenerator } from '@/lib/schema';

interface MovieWatchPageProps {
  params: Promise<{
    id: string;
  }>;
}

async function getMovie(id: string) {
  try {
    const movie = await apiClient.getMovie(id);
    return movie;
  } catch (error) {
    console.error('Error fetching movie:', error);
    return null;
  }
}

export async function generateMetadata({ params }: MovieWatchPageProps): Promise<Metadata> {
  const { id } = await params;
  const movie = await getMovie(id);

  if (!movie) {
    return {
      title: 'Movie Not Found | StreamZen',
      description: 'The requested movie could not be found.',
    };
  }

  return SEOGenerator.generateMovieMetadata(movie);
}

export default async function MovieWatchPage({ params }: MovieWatchPageProps) {
  const { id } = await params;
  const movie = await getMovie(id);

  if (!movie) {
    notFound();
  }

  // Generate all streaming sources
  const vidsrc = VidSrcAPI.getInstance();
  const streamingSources = vidsrc.generateAllMovieEmbedUrls(movie.imdbId, movie.tmdbId);

  const contentInfo = {
    title: movie.title,
    year: movie.year,
    rating: movie.rating,
    runtime: movie.runtime,
    imdbRating: movie.imdbRating,
    description: movie.description,
    genres: movie.genres,
    director: movie.director,
    cast: movie.cast,
    language: movie.language,
    country: movie.country,
    posterUrl: movie.posterUrl,
    type: 'movie' as const
  };

  // Generate structured data
  const movieSchema = SchemaGenerator.generateMovieSchema(movie);
  const breadcrumbSchema = SchemaGenerator.generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Movies', url: '/movies' },
    { name: movie.title, url: `/watch/movie/${movie.imdbId}` }
  ]);

  return (
    <div className="min-h-screen bg-black">
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([
            SchemaGenerator.generateWebsiteSchema(),
            movieSchema,
            breadcrumbSchema
          ])
        }}
      />

      <VideoPlayer
        streamingSources={streamingSources}
        title={movie.title}
        type="movie"
      />

      <div className="max-w-[2560px] mx-auto px-8 lg:px-24 py-12">
        <ContentInfo content={contentInfo} />

        {/* SEO Blog-Style Content */}
        <div className="mt-16 max-w-4xl mx-auto">
          <article className="prose prose-invert prose-lg max-w-none">
            <h2 className="text-3xl font-bold text-white mb-6">
              About {movie.title} ({movie.year})
            </h2>

            <div className="text-gray-300 leading-relaxed space-y-4">
              <p>
                <strong>{movie.title}</strong> is a {movie.genres?.join(', ').toLowerCase()} movie released in {movie.year}.
                {movie.description && ` ${movie.description}`}
              </p>

              {movie.director && (
                <p>
                  Directed by <strong>{movie.director}</strong>, this film showcases exceptional storytelling and cinematography.
                </p>
              )}

              {movie.cast && movie.cast.length > 0 && (
                <p>
                  The movie features an outstanding cast including <strong>{movie.cast.slice(0, 5).join(', ')}</strong>
                  {movie.cast.length > 5 && ' and many more talented actors'}.
                </p>
              )}

              {movie.imdbRating && (
                <p>
                  With an IMDb rating of <strong>{movie.imdbRating}/10</strong>, {movie.title} has received
                  {movie.imdbRating >= 8 ? ' critical acclaim' : movie.imdbRating >= 7 ? ' positive reviews' : ' mixed reviews'}
                  from audiences and critics alike.
                </p>
              )}

              <p>
                Watch <strong>{movie.title}</strong> online free in HD quality on StreamZen. Our platform provides
                multiple streaming sources to ensure the best viewing experience. Enjoy this {movie.genres?.[0]?.toLowerCase()}
                masterpiece with crystal clear video and audio quality.
              </p>

              {movie.language && movie.language !== 'English' && (
                <p>
                  Originally produced in <strong>{movie.language}</strong>, this film offers subtitles and dubbing
                  options for international audiences.
                </p>
              )}

              <p>
                StreamZen offers the latest movies and classic films in various genres. Whether you're looking for
                action-packed blockbusters, heartwarming dramas, spine-chilling horror, or laugh-out-loud comedies,
                our extensive library has something for everyone.
              </p>
            </div>

            <div className="mt-8 p-6 bg-gray-900/50 rounded-lg border border-gray-800">
              <h3 className="text-xl font-semibold text-white mb-4">Why Watch on StreamZen?</h3>
              <ul className="text-gray-300 space-y-2">
                <li>• HD Quality streaming with multiple sources</li>
                <li>• No registration required - watch instantly</li>
                <li>• Compatible with all devices</li>
                <li>• Fast loading and reliable servers</li>
                <li>• Updated daily with latest releases</li>
              </ul>
            </div>
          </article>
        </div>
      </div>
    </div>
  );
}
