import { NextRequest, NextResponse } from 'next/server';
import ContentService from '@/lib/contentService';

const contentService = ContentService.getInstance();

// Simple in-memory cache for frequently requested data
const cache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

function getCacheKey(filters: any): string {
  return JSON.stringify(filters);
}

function getCachedResult(key: string) {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }
  return null;
}

function setCachedResult(key: string, data: any) {
  cache.set(key, {
    data,
    timestamp: Date.now()
  });

  // Clean old cache entries (simple cleanup)
  if (cache.size > 100) {
    const oldestKey = cache.keys().next().value;
    cache.delete(oldestKey);
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const filters = {
      genre: searchParams.get('genre') || undefined,
      year: searchParams.get('year') ? parseInt(searchParams.get('year')!) : undefined,
      language: searchParams.get('language') || undefined,
      country: searchParams.get('country') || undefined,
      rating: searchParams.get('rating') || undefined,
      quality: searchParams.get('quality') || undefined,
      sortBy: (searchParams.get('sortBy') as any) || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1,
      limit: Math.min(searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 24, 50), // Limit max to 50 for performance
      search: searchParams.get('search') || undefined,
    };

    console.log(`🎬 Fetching movies: page ${filters.page}, limit ${filters.limit}, sortBy ${filters.sortBy}`);

    // Add timeout for large dataset queries
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), 30000); // 30 second timeout
    });

    const result = await Promise.race([
      contentService.getMovies(filters),
      timeoutPromise
    ]);

    console.log(`✅ Movies fetched successfully: ${result.data.length} items, total: ${result.pagination.total}`);

    return NextResponse.json(result);
  } catch (error) {
    console.error('❌ Error fetching movies:', error);

    // Return a more helpful error response
    if (error.message === 'Request timeout') {
      return NextResponse.json(
        {
          error: 'Request timeout - try reducing the page size or adding filters',
          suggestion: 'Use smaller limit (e.g., limit=12) or add genre/year filters'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to fetch movies',
        details: error.message
      },
      { status: 500 }
    );
  }
}
