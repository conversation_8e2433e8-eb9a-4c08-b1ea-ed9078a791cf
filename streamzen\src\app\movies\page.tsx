import { Suspense } from 'react';
import { Metadata } from 'next';
import { apiClient } from '@/lib/api';
import ContentGrid from '@/components/ContentGrid';
import FilterSidebar from '@/components/FilterSidebar';
import LoadingSpinner from '@/components/LoadingSpinner';
import ClientSearchBar from '@/components/ClientSearchBar';
import { SEOGenerator } from '@/lib/seo';
import { SchemaGenerator } from '@/lib/schema';

interface MoviesPageProps {
  searchParams: Promise<{
    page?: string;
    genre?: string;
    year?: string;
    language?: string;
    country?: string;
    rating?: string;
    quality?: string;
    sortBy?: string;
    sortOrder?: string;
    search?: string;
  }>;
}

export async function generateMetadata({ searchParams }: MoviesPageProps): Promise<Metadata> {
  const resolvedSearchParams = await searchParams;
  const { genre, year, search, page } = resolvedSearchParams;

  let title = 'Watch Movies Online Free';
  let description = 'Watch the latest movies online free in HD quality. Discover thousands of movies from all genres including action, comedy, drama, horror, thriller, romance and more.';
  let keywords = ['watch movies online', 'free movies', 'HD movies', 'latest movies', 'movie streaming'];

  // Dynamic title and description based on filters
  if (search) {
    title = `Search Results for "${search}" - Movies`;
    description = `Search results for "${search}" movies. Watch ${search} movies online free in HD quality on StreamZen.`;
    keywords.push(search, `${search} movies`, `watch ${search}`);
  } else if (genre && year) {
    title = `${genre} Movies ${year} - Watch Online Free`;
    description = `Watch the best ${genre} movies from ${year} online free in HD quality. Discover top-rated ${genre} films from ${year}.`;
    keywords.push(genre, `${genre} movies`, `${year} movies`, `${genre} ${year}`);
  } else if (genre) {
    title = `${genre} Movies - Watch Online Free`;
    description = `Watch the best ${genre} movies online free in HD quality. Discover top-rated ${genre} films and blockbusters.`;
    keywords.push(genre, `${genre} movies`, `best ${genre} movies`);
  } else if (year) {
    title = `${year} Movies - Watch Online Free`;
    description = `Watch the best movies from ${year} online free in HD quality. Discover top-rated films and blockbusters from ${year}.`;
    keywords.push(`${year} movies`, `movies ${year}`, `best ${year} movies`);
  }

  if (page && parseInt(page) > 1) {
    title += ` - Page ${page}`;
    description += ` Browse page ${page} for more movies.`;
  }

  const path = `/movies${Object.keys(resolvedSearchParams).length > 0 ? '?' + new URLSearchParams(resolvedSearchParams as any).toString() : ''}`;

  return SEOGenerator.generatePageMetadata(title, description, path, keywords);
}

async function getMovies(searchParams: Awaited<MoviesPageProps['searchParams']>) {
  try {
    const filters = {
      page: searchParams.page ? parseInt(searchParams.page) : 1,
      limit: 24,
      genre: searchParams.genre,
      year: searchParams.year ? parseInt(searchParams.year) : undefined,
      language: searchParams.language,
      country: searchParams.country,
      rating: searchParams.rating,
      quality: searchParams.quality,
      sortBy: (searchParams.sortBy as any) || 'createdAt',
      sortOrder: (searchParams.sortOrder as 'asc' | 'desc') || 'desc',
      search: searchParams.search,
    };

    const response = await apiClient.getMovies(filters);
    return response;
  } catch (error) {
    console.error('Error fetching movies:', error);
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 24,
        total: 0,
        pages: 0
      }
    };
  }
}

function transformMovieToContentItem(movie: any) {
  return {
    id: movie._id,
    imdbId: movie.imdbId,
    title: movie.title,
    year: movie.year,
    posterUrl: movie.posterUrl,
    imdbRating: movie.imdbRating,
    description: movie.description,
    type: 'movie' as const
  };
}

export default async function MoviesPage({ searchParams }: MoviesPageProps) {
  const resolvedSearchParams = await searchParams;
  const { data: movies, pagination } = await getMovies(resolvedSearchParams);

  // Generate structured data for SEO
  const collectionSchema = SchemaGenerator.generateCollectionPageSchema(
    resolvedSearchParams.search ? `Search Results for "${resolvedSearchParams.search}"` : 'Movies',
    'Watch the latest movies online free in HD quality. Discover thousands of movies from all genres.',
    `/movies${Object.keys(resolvedSearchParams).length > 0 ? '?' + new URLSearchParams(resolvedSearchParams as any).toString() : ''}`,
    movies.slice(0, 10).map(movie => ({
      name: `${movie.title} (${movie.year})`,
      url: `/watch/movie/${movie.imdbId}`,
      image: movie.posterUrl
    }))
  );

  const breadcrumbSchema = SchemaGenerator.generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Movies', url: '/movies' },
    ...(resolvedSearchParams.search ? [{ name: `Search: ${resolvedSearchParams.search}`, url: `/movies?search=${resolvedSearchParams.search}` }] : []),
    ...(resolvedSearchParams.genre ? [{ name: resolvedSearchParams.genre, url: `/movies?genre=${resolvedSearchParams.genre}` }] : [])
  ]);

  return (
    <div className="min-h-screen bg-black">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([
            SchemaGenerator.generateWebsiteSchema(),
            collectionSchema,
            breadcrumbSchema
          ])
        }}
      />
      {/* Enhanced Hero Header */}
      <div className="relative bg-black border-b border-gray-800/50 overflow-hidden">
        {/* Premium Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-gray-800/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-1/2 right-1/4 w-80 h-80 bg-gray-700/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-[600px] h-32 bg-gradient-to-t from-gray-900/30 to-transparent blur-xl" />
        </div>

        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-16 lg:py-24">
          <div className="mb-12">
            {/* Premium Title with Gradient Text */}
            <div className="mb-6">
              <h1 className="text-5xl lg:text-7xl xl:text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-gray-200 to-gray-400 mb-6 tracking-tight leading-none">
                Movies
              </h1>
              <div className="w-24 h-1 bg-gradient-to-r from-red-500 to-gray-600 rounded-full mb-8"></div>
            </div>

            <p className="text-xl lg:text-2xl text-gray-300 max-w-3xl leading-relaxed font-light mb-8">
              Discover premium movies in stunning quality. Experience cinema like never before with our curated collection of blockbusters, indie gems, and timeless classics.
            </p>


          </div>

          {/* Enhanced Stats Cards */}
          <div className="flex flex-wrap items-center gap-6">
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-red-400 font-bold text-lg">{pagination.total.toLocaleString()}</span>
                <span className="text-gray-400 text-lg">Movies Available</span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                <span className="text-green-400 font-bold text-lg">4K Ultra HD</span>
                <span className="text-gray-400 text-lg">Premium Quality</span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
                <span className="text-blue-400 font-bold text-lg">Daily Updates</span>
                <span className="text-gray-400 text-lg">Fresh Content</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="relative">
        {/* Subtle Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/3 right-1/4 w-72 h-72 bg-gray-800/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/3 left-1/4 w-64 h-64 bg-gray-700/5 rounded-full blur-3xl" />
        </div>

        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-12">
          <div className="flex gap-12">
            {/* Enhanced Sidebar */}
            <div className="w-80 flex-shrink-0">
              <div className="sticky top-8">
                <FilterSidebar
                  currentFilters={resolvedSearchParams}
                  basePath="/movies"
                  contentType="movies"
                />
              </div>
            </div>

            {/* Enhanced Content Grid */}
            <div className="flex-1">
              <div className="mb-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-3xl font-bold text-white tracking-tight">
                    {resolvedSearchParams.search ? `Search Results for "${resolvedSearchParams.search}"` : 'All Movies'}
                  </h2>
                  <div className="glass-elevated px-4 py-2 rounded-xl border border-gray-700/50">
                    <span className="text-gray-300 text-base font-medium">
                      Page {pagination.page} of {pagination.pages}
                    </span>
                  </div>
                </div>

                <Suspense fallback={<LoadingSpinner />}>
                  <ContentGrid
                    items={movies.map(transformMovieToContentItem)}
                    pagination={pagination}
                    basePath="/movies"
                    currentFilters={resolvedSearchParams}
                  />
                </Suspense>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
