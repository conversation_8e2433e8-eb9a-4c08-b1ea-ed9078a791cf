import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { apiClient } from '@/lib/api';
import VideoPlayer from '@/components/VideoPlayer';
import ContentInfo from '@/components/ContentInfo';
import EpisodeSelector from '@/components/EpisodeSelector';
import VidSrcAPI from '@/lib/vidsrc';
import { SEOGenerator } from '@/lib/seo';
import { SchemaGenerator } from '@/lib/schema';

interface SeriesWatchPageProps {
  params: Promise<{
    id: string;
  }>;
  searchParams: Promise<{
    season?: string;
    episode?: string;
  }>;
}

async function getSeriesData(id: string, season?: number) {
  try {
    const [series, episodes] = await Promise.all([
      apiClient.getSeriesById(id),
      apiClient.getSeriesEpisodes(id, season)
    ]);
    return { series, episodes };
  } catch (error) {
    console.error('Error fetching series data:', error);
    return { series: null, episodes: [] };
  }
}

export default async function SeriesWatchPage({ params, searchParams }: SeriesWatchPageProps) {
  const { id } = await params;
  const { season, episode } = await searchParams;
  const selectedSeason = season ? parseInt(season) : 1;
  const selectedEpisode = episode ? parseInt(episode) : 1;

  const { series, episodes } = await getSeriesData(id, selectedSeason);

  if (!series) {
    notFound();
  }

  // Find the current episode
  const currentEpisode = episodes.find(
    ep => ep.season === selectedSeason && ep.episode === selectedEpisode
  );

  const contentInfo = {
    title: series.title,
    year: series.startYear,
    rating: series.rating,
    imdbRating: series.imdbRating,
    description: series.description,
    genres: series.genres,
    creator: series.creator,
    cast: series.cast,
    language: series.language,
    country: series.country,
    posterUrl: series.posterUrl,
    type: 'series' as const,
    totalSeasons: series.totalSeasons,
    status: series.status
  };

  // Generate all streaming sources for the current episode
  const vidsrc = VidSrcAPI.getInstance();
  const streamingSources = vidsrc.generateAllEpisodeEmbedUrls(id, selectedSeason, selectedEpisode, series.tmdbId);

  return (
    <div className="min-h-screen bg-black">
      <VideoPlayer
        streamingSources={streamingSources}
        title={`${series.title} - S${selectedSeason}E${selectedEpisode}`}
        type="series"
      />
      
      <div className="max-w-[2560px] mx-auto px-8 lg:px-24 py-12">
        <div className="grid grid-cols-1 xl:grid-cols-5 gap-16">
          {/* Content Info */}
          <div className="xl:col-span-3">
            <ContentInfo content={contentInfo} />
          </div>

          {/* Episode Selector */}
          <div className="xl:col-span-2">
            <EpisodeSelector
              seriesId={id}
              episodes={episodes}
              currentSeason={selectedSeason}
              currentEpisode={selectedEpisode}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
