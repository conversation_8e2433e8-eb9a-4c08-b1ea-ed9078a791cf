const XLSX = require('xlsx');
const path = require('path');
const mongoose = require('mongoose');
const axios = require('axios');
const cheerio = require('cheerio');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Mass parallel scraper for all 18,950 series
class MassSeriesScraper {
  constructor() {
    this.PARALLEL_LIMIT = 50; // 50 concurrent requests
    this.BATCH_SIZE = 100; // Process 100 series per batch
    this.DELAY_BETWEEN_BATCHES = 3000; // 3 second delay between batches
    this.processedCount = 0;
    this.successCount = 0;
    this.errorCount = 0;
    this.skippedCount = 0;
    this.startTime = Date.now();
    this.results = {
      series: [],
      errors: []
    };
  }

  async initialize() {
    console.log('🚀 Initializing Mass Series Scraper for 18,950 series...');
    console.log(`💻 System: Using ${this.PARALLEL_LIMIT} parallel processes`);
    
    // Connect to MongoDB
    if (!mongoose.connection.readyState) {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';
      console.log('🔗 Connecting to MongoDB...');
      await mongoose.connect(mongoUri);
      console.log('✅ Connected to MongoDB');
    }

    // Define series schema inline
    const seriesSchema = new mongoose.Schema({
      imdbId: { type: String, required: true, unique: true },
      title: { type: String, required: true },
      startYear: { type: Number, required: true },
      endYear: Number,
      description: String,
      genres: [String],
      cast: [String],
      creator: String,
      language: String,
      country: String,
      imdbRating: Number,
      imdbVotes: String,
      posterUrl: String,
      backdropUrl: String,
      trailerUrl: String,
      embedUrl: String,
      vidsrcUrl: String,
      totalSeasons: Number,
      status: String,
      rating: String, // MPAA rating
      popularity: Number,
      popularityDelta: Number,
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });

    // Create model
    this.Series = mongoose.models.Series || mongoose.model('Series', seriesSchema);
  }

  async loadExcelFile() {
    console.log('📊 Loading Excel file with 18,950 series...');
    
    const excelPath = path.join(__dirname, 'imdb_movie_ids_finalseries.xlsx');
    const workbook = XLSX.readFile(excelPath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);
    
    const imdbIds = data.map(row => row.TitleID).filter(id => id && id.startsWith('tt'));
    
    console.log(`✅ Loaded ${imdbIds.length} IMDb IDs from Excel file`);
    return imdbIds;
  }

  getRandomUserAgent() {
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ];
    return userAgents[Math.floor(Math.random() * userAgents.length)];
  }

  async fetchPage(imdbId) {
    const url = `https://www.imdb.com/title/${imdbId}/`;
    const headers = {
      'User-Agent': this.getRandomUserAgent(),
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Cache-Control': 'max-age=0',
    };

    const response = await axios.get(url, { headers, timeout: 30000 });
    return cheerio.load(response.data);
  }

  extractBasicInfo($) {
    const titleElement = $('h1[data-testid="hero__pageTitle"] span[data-testid="hero__primary-text"]');
    const title = titleElement.text().trim();
    
    if (!title) {
      throw new Error('Could not extract title from IMDb page');
    }

    const yearElement = $('ul.ipc-inline-list a[href*="/releaseinfo/"]');
    const yearText = yearElement.text().trim();
    const year = parseInt(yearText) || new Date().getFullYear();

    const typeIndicators = $('ul.ipc-inline-list li').text().toLowerCase();
    const isSeries = typeIndicators.includes('tv series') || typeIndicators.includes('tv mini series');
    
    return { title, year, type: isSeries ? 'series' : 'movie' };
  }

  extractGenres($) {
    const genres = [];
    const genreSelectors = [
      '.ipc-chip-list--baseAlt .ipc-chip .ipc-chip__text',
      '[data-testid="genres"] .ipc-chip .ipc-chip__text',
      '[data-testid="genres"] .ipc-chip__text',
      'li[data-testid="storyline-genres"] .ipc-metadata-list-item__list-content-item'
    ];

    for (const selector of genreSelectors) {
      const elements = $(selector);
      if (elements.length > 0) {
        elements.each((_, element) => {
          const genre = $(element).text().trim();
          if (genre && genre.length > 0 && genre.length < 50 && !genres.includes(genre)) {
            const skipTexts = ['Genres', 'Genre', 'See all', 'More', 'All', '...'];
            if (!skipTexts.includes(genre)) {
              genres.push(genre);
            }
          }
        });
        if (genres.length > 0) break;
      }
    }
    return genres;
  }

  extractCast($) {
    const cast = [];
    const castSelectors = [
      'section[data-testid="title-cast"] a[data-testid="title-cast-item__actor"]',
      '.cast_list .primary_photo + td a'
    ];

    for (const selector of castSelectors) {
      const elements = $(selector);
      if (elements.length > 0) {
        elements.each((_, element) => {
          const actorName = $(element).text().trim();
          if (actorName && !cast.includes(actorName) && cast.length < 10) {
            cast.push(actorName);
          }
        });
        break;
      }
    }
    return cast;
  }

  extractIMDbRating($) {
    const ratingElement = $('div[data-testid="hero-rating-bar__aggregate-rating__score"] span');
    const rating = parseFloat(ratingElement.text().trim()) || undefined;
    
    const votesElement = $('div.sc-d541859f-3');
    const votes = votesElement.text().trim() || undefined;
    
    return { rating, votes };
  }

  extractDescription($) {
    const selectors = [
      'span[data-testid="plot-xl"]',
      'span[data-testid="plot-l"]',
      'span[data-testid="plot"]'
    ];

    for (const selector of selectors) {
      const element = $(selector);
      const text = element.text().trim();
      if (text && text.length > 10) return text;
    }
    return undefined;
  }

  extractRating($) {
    const ratingElement = $('ul.ipc-inline-list a[href*="/parentalguide/"]');
    return ratingElement.text().trim() || undefined;
  }

  extractCreator($) {
    const creatorSelectors = [
      'li[data-testid="title-pc-principal-credit"]:contains("Creator") .ipc-metadata-list-item__list-content-item',
      'li[data-testid="title-pc-principal-credit"]:contains("Creators") .ipc-metadata-list-item__list-content-item',
      '.credit_summary_item:contains("Creator") a',
      '.credit_summary_item:contains("Created by") a'
    ];

    for (const selector of creatorSelectors) {
      const element = $(selector).first();
      const creator = element.text().trim();
      if (creator) return creator;
    }
    return undefined;
  }

  extractLanguage($) {
    const languageSelectors = [
      'li[data-testid="title-details-languages"] .ipc-metadata-list-item__list-content-item',
      'div[data-testid="title-details-section"] li:contains("Language") .ipc-metadata-list-item__list-content-item'
    ];

    for (const selector of languageSelectors) {
      const element = $(selector).first();
      const language = element.text().trim();
      if (language) return language;
    }
    return undefined;
  }

  extractCountry($) {
    const countrySelectors = [
      'li[data-testid="title-details-origin"] .ipc-metadata-list-item__list-content-item',
      'div[data-testid="title-details-section"] li:contains("Country") .ipc-metadata-list-item__list-content-item'
    ];

    for (const selector of countrySelectors) {
      const element = $(selector).first();
      const country = element.text().trim();
      if (country) return country;
    }
    return undefined;
  }

  extractEndYear($) {
    // Try to extract end year from series info
    const yearElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < yearElements.length; i++) {
      const text = $(yearElements[i]).text().trim();
      const yearMatch = text.match(/(\d{4})–(\d{4})/);
      if (yearMatch) {
        return parseInt(yearMatch[2]);
      }
    }
    return undefined;
  }

  extractTotalSeasons($) {
    // Try to extract total seasons from series info
    const seasonElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < seasonElements.length; i++) {
      const text = $(seasonElements[i]).text().trim();
      const seasonMatch = text.match(/(\d+)\s+seasons?/i);
      if (seasonMatch) {
        return parseInt(seasonMatch[1]);
      }
    }
    return 1; // Default to 1 season
  }

  extractSeriesStatus($) {
    // Try to determine if series is ongoing or ended
    const yearElements = $('ul.ipc-inline-list li');
    for (let i = 0; i < yearElements.length; i++) {
      const text = $(yearElements[i]).text().trim();
      if (text.includes('–') && !text.match(/\d{4}–\d{4}/)) {
        return 'ongoing';
      } else if (text.match(/\d{4}–\d{4}/)) {
        return 'ended';
      }
    }
    return 'ongoing'; // Default to ongoing
  }

  extractPopularity($) {
    const popularityElement = $('div[data-testid="hero-rating-bar__popularity__score"]');
    const popularity = parseInt(popularityElement.text().trim()) || undefined;
    
    const deltaElement = $('div[data-testid="hero-rating-bar__popularity__delta"]');
    const deltaText = deltaElement.text().trim();
    const delta = deltaText ? parseInt(deltaText.replace(/[^\d-]/g, '')) : undefined;
    
    return { popularity, delta };
  }

  extractPosterUrl($) {
    const selectors = [
      'div[data-testid="hero-media__poster"] img',
      '.ipc-image[data-testid="hero-media__poster"]',
      '.poster img'
    ];

    for (const selector of selectors) {
      const element = $(selector);
      const src = element.attr('src');
      if (src && src.includes('media-amazon.com')) {
        return src.replace(/\._.*?_\./, '._V1_FMjpg_UX1000_.');
      }
    }
    return undefined;
  }

  extractBackdropUrl($) {
    const selectors = [
      '.hero-media__slate-overlay img',
      '.slate img',
      '.hero__background img',
      'div[data-testid="hero-media"] img'
    ];

    for (const selector of selectors) {
      const element = $(selector);
      const src = element.attr('src');
      if (src && src.includes('media-amazon.com')) {
        return src.replace(/\._.*?_\./, '._V1_FMjpg_UX1920_.');
      }
    }
    return undefined;
  }

  extractTrailerInfo($) {
    const trailerElement = $('a[data-testid="video-player-slate-overlay"]');
    const url = trailerElement.attr('href') || undefined;
    return { url };
  }

  generateVidSrcUrl(imdbId) {
    return `https://vidsrc.me/embed/tv?imdb=${imdbId}&season=1&episode=1`;
  }

  async scrapeContent(imdbId) {
    console.log(`🔍 [${imdbId}] Starting scrape process...`);

    try {
      // First check if series already exists in database
      console.log(`🔍 [${imdbId}] Checking if series exists in database...`);
      const existingSeries = await this.Series.findOne({ imdbId }).lean();
      if (existingSeries) {
        console.log(`⏭️ [${imdbId}] Series already exists: "${existingSeries.title}" - SKIPPING`);
        return {
          success: true,
          type: 'series',
          data: existingSeries,
          skipped: true // Mark as skipped to avoid re-saving
        };
      }

      console.log(`🌐 [${imdbId}] Series not found in DB, fetching from IMDb...`);
      const $ = await this.fetchPage(imdbId);

      console.log(`📄 [${imdbId}] Page fetched, extracting basic info...`);
      const basicInfo = this.extractBasicInfo($);
      console.log(`📋 [${imdbId}] Basic info: "${basicInfo.title}" (${basicInfo.year}) - Type: ${basicInfo.type}`);

      // Skip if it's not a series
      if (basicInfo.type !== 'series') {
        console.log(`❌ [${imdbId}] Not a TV series (detected as ${basicInfo.type}) - SKIPPING`);
        return {
          success: false,
          error: `Not a TV series (detected as ${basicInfo.type})`,
          imdbId
        };
      }

      console.log(`📊 [${imdbId}] Extracting comprehensive data...`);

      const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);
      console.log(`⭐ [${imdbId}] IMDb Rating: ${imdbRating || 'N/A'} (${imdbVotes || 'N/A'} votes)`);

      const { popularity, delta: popularityDelta } = this.extractPopularity($);
      const { url: trailerUrl } = this.extractTrailerInfo($);

      const genres = this.extractGenres($);
      console.log(`🎭 [${imdbId}] Genres: ${genres.length > 0 ? genres.join(', ') : 'None found'}`);

      const cast = this.extractCast($);
      console.log(`🎬 [${imdbId}] Cast: ${cast.length > 0 ? cast.slice(0, 3).join(', ') + (cast.length > 3 ? '...' : '') : 'None found'}`);

      const creator = this.extractCreator($);
      console.log(`👨‍💼 [${imdbId}] Creator: ${creator || 'Not found'}`);

      const language = this.extractLanguage($);
      const country = this.extractCountry($);
      console.log(`🌍 [${imdbId}] Language: ${language || 'N/A'}, Country: ${country || 'N/A'}`);

      const totalSeasons = this.extractTotalSeasons($);
      const status = this.extractSeriesStatus($);
      console.log(`📺 [${imdbId}] Seasons: ${totalSeasons || 'N/A'}, Status: ${status || 'N/A'}`);

      const posterUrl = this.extractPosterUrl($);
      console.log(`🖼️ [${imdbId}] Poster: ${posterUrl ? 'Found' : 'Not found'}`);

      const seriesData = {
        imdbId,
        title: basicInfo.title,
        startYear: basicInfo.year,
        endYear: this.extractEndYear($),
        rating: this.extractRating($),
        imdbRating,
        imdbVotes,
        popularity,
        popularityDelta,
        description: this.extractDescription($),
        genres,
        cast,
        creator,
        language,
        country,
        posterUrl,
        backdropUrl: this.extractBackdropUrl($),
        trailerUrl,
        embedUrl: this.generateVidSrcUrl(imdbId),
        vidsrcUrl: this.generateVidSrcUrl(imdbId),
        totalSeasons,
        status,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      console.log(`✅ [${imdbId}] Successfully scraped: "${basicInfo.title}" with ${genres.length} genres, ${cast.length} cast members`);

      return {
        success: true,
        type: 'series',
        data: seriesData
      };
    } catch (error) {
      console.error(`❌ [${imdbId}] SCRAPING FAILED:`);
      console.error(`   Error Type: ${error.name || 'Unknown'}`);
      console.error(`   Error Message: ${error.message}`);
      console.error(`   Stack Trace: ${error.stack}`);

      // Categorize error types for better debugging
      let errorCategory = 'Unknown';
      if (error.message.includes('timeout')) {
        errorCategory = 'Timeout';
      } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
        errorCategory = 'Network';
      } else if (error.message.includes('Could not extract title')) {
        errorCategory = 'Parsing';
      } else if (error.message.includes('404')) {
        errorCategory = 'Not Found';
      } else if (error.message.includes('403') || error.message.includes('429')) {
        errorCategory = 'Rate Limited';
      }

      console.error(`   Error Category: ${errorCategory}`);

      return {
        success: false,
        error: `${errorCategory}: ${error.message}`,
        imdbId,
        errorDetails: {
          type: error.name,
          message: error.message,
          category: errorCategory,
          stack: error.stack
        }
      };
    }
  }

  async processBatch(imdbIds, batchIndex) {
    console.log(`\n🔄 ========== BATCH ${batchIndex} START ==========`);
    console.log(`📦 Processing ${imdbIds.length} series: ${imdbIds.slice(0, 5).join(', ')}${imdbIds.length > 5 ? '...' : ''}`);

    const batchStartTime = Date.now();

    // Create promises with staggered delays
    const promises = imdbIds.map(async (imdbId, index) => {
      // Add staggered delay to avoid overwhelming IMDb
      await new Promise(resolve => setTimeout(resolve, index * 100)); // 100ms between each request
      return this.scrapeContent(imdbId);
    });

    console.log(`⚡ Processing with ${this.PARALLEL_LIMIT} parallel workers...`);

    // Process with controlled concurrency
    const results = [];
    for (let i = 0; i < promises.length; i += this.PARALLEL_LIMIT) {
      const batch = promises.slice(i, i + this.PARALLEL_LIMIT);
      const chunkStartTime = Date.now();
      console.log(`🔧 Processing chunk ${Math.floor(i/this.PARALLEL_LIMIT) + 1}/${Math.ceil(promises.length/this.PARALLEL_LIMIT)} (${batch.length} items)...`);

      const batchResults = await Promise.allSettled(batch);
      results.push(...batchResults);

      const chunkDuration = Date.now() - chunkStartTime;
      console.log(`✅ Chunk completed in ${chunkDuration}ms`);
    }
    
    console.log(`📊 Analyzing batch results...`);

    // Process results with detailed logging
    let batchNewCount = 0;
    let batchSkippedCount = 0;
    let batchErrorCount = 0;
    const batchErrors = [];

    results.forEach((result, index) => {
      this.processedCount++;
      const imdbId = imdbIds[index];

      if (result.status === 'fulfilled' && result.value.success) {
        this.successCount++;
        // Track skipped vs new items
        if (result.value.skipped) {
          this.skippedCount++;
          batchSkippedCount++;
        } else {
          this.results.series.push(result.value.data);
          batchNewCount++;
          console.log(`🆕 [${imdbId}] NEW SERIES: "${result.value.data.title}"`);
        }
      } else {
        this.errorCount++;
        batchErrorCount++;
        const errorMsg = result.status === 'rejected' ? result.reason.message : result.value.error;
        console.error(`❌ [${imdbId}] FAILED: ${errorMsg}`);

        this.results.errors.push({
          imdbId,
          error: errorMsg,
          details: result.value?.errorDetails
        });
        batchErrors.push({ imdbId, error: errorMsg });
      }
    });

    const batchDuration = Date.now() - batchStartTime;

    console.log(`\n📈 BATCH ${batchIndex} SUMMARY:`);
    console.log(`   ⏱️  Duration: ${batchDuration}ms`);
    console.log(`   🆕 New Series: ${batchNewCount}`);
    console.log(`   ⏭️  Skipped: ${batchSkippedCount}`);
    console.log(`   ❌ Errors: ${batchErrorCount}`);

    if (batchErrors.length > 0) {
      console.log(`\n🔍 BATCH ${batchIndex} ERROR DETAILS:`);
      batchErrors.forEach(({ imdbId, error }) => {
        console.log(`   ❌ ${imdbId}: ${error}`);
      });
    }

    this.logProgress();

    // Save batch to database immediately
    console.log(`💾 Saving batch ${batchIndex} to database...`);
    await this.saveBatchToDatabase();

    console.log(`🔄 ========== BATCH ${batchIndex} END ==========\n`);
  }

  async saveBatchToDatabase() {
    if (this.results.series.length > 0) {
      console.log(`💾 Preparing to save ${this.results.series.length} new series to database...`);

      try {
        const operations = this.results.series.map(seriesData => {
          console.log(`📝 Preparing DB operation for: ${seriesData.title} (${seriesData.imdbId})`);
          return {
            updateOne: {
              filter: { imdbId: seriesData.imdbId },
              update: seriesData,
              upsert: true
            }
          };
        });

        console.log(`🔄 Executing bulk write operation with ${operations.length} operations...`);
        const result = await this.Series.bulkWrite(operations);

        console.log(`✅ Database operation completed:`);
        console.log(`   📊 Matched: ${result.matchedCount}`);
        console.log(`   🆕 Inserted: ${result.insertedCount}`);
        console.log(`   📝 Modified: ${result.modifiedCount}`);
        console.log(`   ⬆️  Upserted: ${result.upsertedCount}`);

        // Log each saved series
        this.results.series.forEach(series => {
          console.log(`✅ SAVED: "${series.title}" (${series.imdbId}) - ${series.startYear}`);
        });

        this.results.series = []; // Clear after saving
        console.log(`🧹 Cleared batch results array`);

      } catch (error) {
        console.error(`❌ DATABASE SAVE ERROR:`);
        console.error(`   Error Type: ${error.name}`);
        console.error(`   Error Message: ${error.message}`);
        console.error(`   Error Code: ${error.code}`);
        console.error(`   Stack: ${error.stack}`);

        // Log which series failed to save
        console.error(`📋 Failed to save these series:`);
        this.results.series.forEach(series => {
          console.error(`   ❌ ${series.title} (${series.imdbId})`);
        });
      }
    } else {
      console.log(`⏭️ No new series to save in this batch`);
    }
  }

  logProgress() {
    const elapsed = (Date.now() - this.startTime) / 1000;
    const rate = this.processedCount / elapsed;
    const eta = this.totalItems ? (this.totalItems - this.processedCount) / rate : 0;

    console.log(`📈 Progress: ${this.processedCount}/${this.totalItems || '?'} | ✅ ${this.successCount} (${this.skippedCount} skipped) | ❌ ${this.errorCount} | Rate: ${rate.toFixed(2)}/s | ETA: ${Math.round(eta/60)}min`);
  }

  async run() {
    try {
      await this.initialize();
      
      const imdbIds = await this.loadExcelFile();
      this.totalItems = imdbIds.length;
      
      console.log(`🚀 Starting mass parallel scraping of ${imdbIds.length} series...`);
      
      // Split into batches
      const batches = [];
      for (let i = 0; i < imdbIds.length; i += this.BATCH_SIZE) {
        batches.push(imdbIds.slice(i, i + this.BATCH_SIZE));
      }
      
      console.log(`📦 Created ${batches.length} batches of ${this.BATCH_SIZE} series each`);
      
      // Process batches sequentially to avoid overwhelming
      for (let i = 0; i < batches.length; i++) {
        await this.processBatch(batches[i], i + 1);
        
        // Add delay between batches
        if (i < batches.length - 1) {
          console.log(`⏳ Waiting ${this.DELAY_BETWEEN_BATCHES}ms before next batch...`);
          await new Promise(resolve => setTimeout(resolve, this.DELAY_BETWEEN_BATCHES));
        }
      }
      
      console.log('🎉 All series batches completed!');
      
      // Generate final report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Series scraper failed:', error);
    } finally {
      await mongoose.disconnect();
    }
  }

  generateReport() {
    const totalTime = (Date.now() - this.startTime) / 1000;
    const avgRate = this.processedCount / totalTime;
    
    console.log('\\n🎯 FINAL SERIES SCRAPING REPORT');
    console.log('==============================');
    console.log(`📊 Total Processed: ${this.processedCount}`);
    console.log(`✅ Successful: ${this.successCount}`);
    console.log(`⏭️  Skipped (Already Exist): ${this.skippedCount}`);
    console.log(`🆕 New Series Added: ${this.successCount - this.skippedCount}`);
    console.log(`❌ Errors: ${this.errorCount}`);
    console.log(`⏱️  Total Time: ${Math.round(totalTime/60)} minutes`);
    console.log(`⚡ Average Rate: ${avgRate.toFixed(2)} items/second`);
    console.log(`🔧 Parallel Processes: ${this.PARALLEL_LIMIT}`);
    console.log('==============================\\n');
  }
}

// Run the mass series scraper
async function main() {
  const scraper = new MassSeriesScraper();
  await scraper.run();
  process.exit(0);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = MassSeriesScraper;
