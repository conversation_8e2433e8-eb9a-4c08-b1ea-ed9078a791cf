import { Suspense } from 'react';
import { Metadata } from 'next';
import HeroCarousel from '@/components/HeroCarousel';
import ContentSection from '@/components/ContentSection';
import { apiClient } from '@/lib/api';
import InitializeButton from '@/components/ClientInitializeButton';
import { SchemaGenerator } from '@/lib/schema';

async function getHomePageData() {
  try {
    const [moviesResponse, seriesResponse, episodesResponse] = await Promise.all([
      apiClient.getMovies({ limit: 10, sortBy: 'imdbRating', sortOrder: 'desc' }),
      apiClient.getSeries({ limit: 10, sortBy: 'imdbRating', sortOrder: 'desc' }),
      apiClient.getEpisodes({ limit: 10, sortBy: 'createdAt', sortOrder: 'desc' })
    ]);

    return {
      movies: moviesResponse.data,
      series: seriesResponse.data,
      episodes: episodesResponse.data
    };
  } catch (error) {
    console.error('Error fetching homepage data:', error);
    return {
      movies: [],
      series: [],
      episodes: []
    };
  }
}

function transformMovieToHeroItem(movie: any) {
  return {
    id: movie._id,
    imdbId: movie.imdbId,
    title: movie.title,
    year: movie.year,
    posterUrl: movie.posterUrl,
    imdbRating: movie.imdbRating,
    description: movie.description,
    type: 'movie' as const
  };
}

function transformSeriesToHeroItem(series: any) {
  return {
    id: series._id,
    imdbId: series.imdbId,
    title: series.title,
    year: series.startYear,
    posterUrl: series.posterUrl,
    imdbRating: series.imdbRating,
    description: series.description,
    type: 'series' as const
  };
}

function transformMovieToContentItem(movie: any) {
  return {
    id: movie._id,
    imdbId: movie.imdbId,
    title: movie.title,
    year: movie.year,
    posterUrl: movie.posterUrl,
    imdbRating: movie.imdbRating,
    description: movie.description,
    type: 'movie' as const
  };
}

function transformSeriesToContentItem(series: any) {
  return {
    id: series._id,
    imdbId: series.imdbId,
    title: series.title,
    year: series.startYear,
    posterUrl: series.posterUrl,
    imdbRating: series.imdbRating,
    description: series.description,
    type: 'series' as const
  };
}

function transformEpisodeToContentItem(episode: any) {
  return {
    id: episode._id,
    imdbId: episode.imdbId,
    title: episode.episodeTitle || `Episode ${episode.episode}`,
    season: episode.season,
    episode: episode.episode,
    seriesTitle: episode.seriesTitle,
    posterUrl: episode.posterUrl,
    seriesPosterUrl: episode.seriesPosterUrl, // Use series poster for episodes
    imdbRating: episode.imdbRating,
    description: episode.description,
    type: 'episode' as const
  };
}

export default async function Home() {
  const { movies, series, episodes } = await getHomePageData();

  // If no data, show initialization prompt
  if (movies.length === 0 && series.length === 0 && episodes.length === 0) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">Welcome to StreamZen</h1>
          <p className="text-gray-400 mb-8">Initialize the database to start streaming</p>
          <InitializeButton />
        </div>
      </div>
    );
  }

  // Create hero items from top-rated movies and series
  const heroItems = [
    ...movies.slice(0, 3).map(transformMovieToHeroItem),
    ...series.slice(0, 2).map(transformSeriesToHeroItem)
  ];

  return (
    <div className="min-h-screen bg-black">
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([
            SchemaGenerator.generateWebsiteSchema(),
            SchemaGenerator.generateCollectionPageSchema(
              'Latest Movies and TV Series',
              'Watch the latest movies and TV series online free in HD quality. Discover trending content updated daily.',
              '/',
              [
                ...movies.slice(0, 5).map(movie => ({
                  name: `${movie.title} (${movie.year})`,
                  url: `/watch/movie/${movie.imdbId}`,
                  image: movie.posterUrl
                })),
                ...series.slice(0, 5).map(show => ({
                  name: `${show.title} (${show.startYear})`,
                  url: `/watch/series/${show.imdbId}`,
                  image: show.posterUrl
                }))
              ]
            )
          ])
        }}
      />

      {/* Enhanced Hero Section */}
      <HeroCarousel items={heroItems} />

      {/* Premium Content Sections */}
      <div className="relative">
        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gray-800/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gray-700/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }} />
        </div>

        <div className="relative space-y-16 py-16 pb-24">
          <ContentSection
            title="🎬 Latest Movies"
            items={movies.map(transformMovieToContentItem)}
            viewAllHref="/movies"
            className="animate-fade-in"
          />

          <ContentSection
            title="📺 Latest Series"
            items={series.map(transformSeriesToContentItem)}
            viewAllHref="/series"
            className="animate-fade-in"
            style={{ animationDelay: '0.2s' }}
          />

          <ContentSection
            title="🆕 New Episodes"
            items={episodes.map(transformEpisodeToContentItem)}
            viewAllHref="/episodes"
            className="animate-fade-in"
            style={{ animationDelay: '0.4s' }}
          />
        </div>
      </div>
    </div>
  );
}
