{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n\nexport interface Movie {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  year: number;\n  rating?: string;\n  runtime?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Series {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Episode {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  seriesTitle: string;\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  airDate?: string;\n  runtime?: string;\n  imdbRating?: number;\n  description?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    pages: number;\n  };\n}\n\nexport interface ContentFilters {\n  genre?: string;\n  year?: number;\n  language?: string;\n  country?: string;\n  rating?: string;\n  quality?: string;\n  sortBy?: 'title' | 'year' | 'imdbRating' | 'popularity' | 'createdAt';\n  sortOrder?: 'asc' | 'desc';\n  page?: number;\n  limit?: number;\n  search?: string;\n}\n\nexport interface GenreCount {\n  genre: string;\n  count: number;\n}\n\nexport interface LanguageCount {\n  language: string;\n  count: number;\n}\n\nexport interface CountryCount {\n  country: string;\n  count: number;\n}\n\nexport interface FilterOptions {\n  genres: GenreCount[];\n  languages: LanguageCount[];\n  countries: CountryCount[];\n  years: number[];\n  ratings: string[];\n  qualities: string[];\n}\n\nclass ApiClient {\n  private baseUrl: string;\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl;\n  }\n\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    // For server-side rendering, use absolute URL\n    const baseUrl = this.baseUrl || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n    const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options?.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed: ${url}`, error);\n      throw error;\n    }\n  }\n\n  // Movies\n  async getMovies(filters: ContentFilters = {}): Promise<PaginatedResponse<Movie>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Movie>>(`/api/movies?${params.toString()}`);\n  }\n\n  async getMovie(id: string): Promise<Movie> {\n    return this.request<Movie>(`/api/movies/${id}`);\n  }\n\n  // Series\n  async getSeries(filters: ContentFilters = {}): Promise<PaginatedResponse<Series>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Series>>(`/api/series?${params.toString()}`);\n  }\n\n  async getSeriesById(id: string): Promise<Series> {\n    return this.request<Series>(`/api/series/${id}`);\n  }\n\n  async getSeriesEpisodes(id: string, season?: number): Promise<Episode[]> {\n    const params = season ? `?season=${season}` : '';\n    return this.request<Episode[]>(`/api/series/${id}/episodes${params}`);\n  }\n\n  // Episodes\n  async getEpisodes(filters: ContentFilters = {}): Promise<PaginatedResponse<Episode>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Episode>>(`/api/episodes?${params.toString()}`);\n  }\n\n  // Requests\n  async createBulkRequest(imdbIds: string[], contentType: 'auto' | 'movie' | 'series' = 'auto'): Promise<{ requestId: string; status: string; totalCount: number; message: string }> {\n    return this.request('/api/requests', {\n      method: 'POST',\n      body: JSON.stringify({ imdbIds, contentType }),\n    });\n  }\n\n  async getRequestStatus(requestId: string): Promise<any> {\n    return this.request(`/api/requests/${requestId}`);\n  }\n\n  // Filter Options\n  async getMovieFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/movies/filters');\n  }\n\n  async getSeriesFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/series/filters');\n  }\n\n  async getEpisodeFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/episodes/filters');\n  }\n\n  // Search\n  async search(query: string, type: 'all' | 'movies' | 'series' | 'episodes' = 'all', page: number = 1, limit: number = 20): Promise<any> {\n    const params = new URLSearchParams({\n      q: query,\n      type,\n      page: page.toString(),\n      limit: limit.toString()\n    });\n    return this.request(`/api/search?${params.toString()}`);\n  }\n\n  async getSearchSuggestions(query: string, limit: number = 8): Promise<any> {\n    const params = new URLSearchParams({\n      q: query,\n      limit: limit.toString()\n    });\n    return this.request(`/api/search/suggestions?${params.toString()}`);\n  }\n\n  // Sync\n  async syncContent(): Promise<{ success: boolean; message: string; counts: { movies: number; series: number; episodes: number } }> {\n    return this.request('/api/sync', {\n      method: 'POST',\n    });\n  }\n}\n\nexport const apiClient = new ApiClient();\nexport default ApiClient;\n"], "names": [], "mappings": ";;;;AAAA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI,CAAC,6EAAyD,uBAAuB;AA8HzI,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,8CAA8C;QAC9C,MAAM,UAAU,IAAI,CAAC,OAAO,IAAI,CAAC,6EAAyD,uBAAuB;QACjH,MAAM,MAAM,SAAS,UAAU,CAAC,UAAU,WAAW,GAAG,UAAU,UAAU;QAE5E,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,gBAAgB;oBAChB,GAAG,SAAS,OAAO;gBACrB;gBACA,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,EAAE;YAC5C,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAqC;QAC/E,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA2B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IAClF;IAEA,MAAM,SAAS,EAAU,EAAkB;QACzC,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,YAAY,EAAE,IAAI;IAChD;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAsC;QAChF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA4B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IACnF;IAEA,MAAM,cAAc,EAAU,EAAmB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAS,CAAC,YAAY,EAAE,IAAI;IACjD;IAEA,MAAM,kBAAkB,EAAU,EAAE,MAAe,EAAsB;QACvE,MAAM,SAAS,SAAS,CAAC,QAAQ,EAAE,QAAQ,GAAG;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAY,CAAC,YAAY,EAAE,GAAG,SAAS,EAAE,QAAQ;IACtE;IAEA,WAAW;IACX,MAAM,YAAY,UAA0B,CAAC,CAAC,EAAuC;QACnF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA6B,CAAC,cAAc,EAAE,OAAO,QAAQ,IAAI;IACtF;IAEA,WAAW;IACX,MAAM,kBAAkB,OAAiB,EAAE,cAA2C,MAAM,EAAuF;QACjL,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAS;YAAY;QAC9C;IACF;IAEA,MAAM,iBAAiB,SAAiB,EAAgB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,WAAW;IAClD;IAEA,iBAAiB;IACjB,MAAM,wBAAgD;QACpD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,0BAAkD;QACtD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,SAAS;IACT,MAAM,OAAO,KAAa,EAAE,OAAiD,KAAK,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAgB;QACtI,MAAM,SAAS,IAAI,gBAAgB;YACjC,GAAG;YACH;YACA,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IACxD;IAEA,MAAM,qBAAqB,KAAa,EAAE,QAAgB,CAAC,EAAgB;QACzE,MAAM,SAAS,IAAI,gBAAgB;YACjC,GAAG;YACH,OAAO,MAAM,QAAQ;QACvB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,wBAAwB,EAAE,OAAO,QAAQ,IAAI;IACpE;IAEA,OAAO;IACP,MAAM,cAA4H;QAChI,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;YAC/B,QAAQ;QACV;IACF;AACF;AAEO,MAAM,YAAY,IAAI;uCACd", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentGrid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ContentGrid.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ContentGrid.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentGrid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ContentGrid.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ContentGrid.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/FilterSidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/FilterSidebar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/FilterSidebar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/FilterSidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/FilterSidebar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/FilterSidebar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\nimport { Loader2 } from 'lucide-react';\n\nconst LoadingSpinner: React.FC = () => {\n  return (\n    <div className=\"flex items-center justify-center py-16\">\n      <div className=\"text-center\">\n        <Loader2 className=\"animate-spin text-white mx-auto mb-4\" size={48} />\n        <p className=\"text-gray-400\">Loading content...</p>\n      </div>\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,iBAA2B;IAC/B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;oBAAuC,MAAM;;;;;;8BAChE,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIrC;uCAEe", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/seo.ts"], "sourcesContent": ["import { Metadata } from 'next';\nimport { IMovie } from '@/models/Movie';\nimport { ISeries } from '@/models/Series';\nimport { IEpisode } from '@/models/Episode';\n\nexport interface SEOConfig {\n  title: string;\n  description: string;\n  keywords?: string[];\n  canonical?: string;\n  ogImage?: string;\n  ogType?: 'website' | 'video.movie' | 'video.tv_show' | 'video.episode';\n  publishedTime?: string;\n  modifiedTime?: string;\n  authors?: string[];\n  section?: string;\n  tags?: string[];\n}\n\nexport class SEOGenerator {\n  private static baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://streamzen.com';\n  private static siteName = 'StreamZen';\n  private static defaultDescription = 'Premium streaming platform with the latest movies, TV series, and episodes. Watch HD content with multiple streaming sources.';\n\n  static generateMetadata(config: SEOConfig): Metadata {\n    const {\n      title,\n      description,\n      keywords = [],\n      canonical,\n      ogImage,\n      ogType = 'website',\n      publishedTime,\n      modifiedTime,\n      authors = [],\n      section,\n      tags = []\n    } = config;\n\n    const fullTitle = title.includes(this.siteName) ? title : `${title} | ${this.siteName}`;\n    const url = canonical ? `${this.baseUrl}${canonical}` : this.baseUrl;\n    const defaultImage = `${this.baseUrl}/og-default.jpg`;\n\n    return {\n      title: fullTitle,\n      description,\n      keywords: keywords.join(', '),\n      authors: authors.map(name => ({ name })),\n      creator: this.siteName,\n      publisher: this.siteName,\n      formatDetection: {\n        email: false,\n        address: false,\n        telephone: false,\n      },\n      metadataBase: new URL(this.baseUrl),\n      alternates: {\n        canonical: url,\n      },\n      openGraph: {\n        title: fullTitle,\n        description,\n        url,\n        siteName: this.siteName,\n        images: [\n          {\n            url: ogImage || defaultImage,\n            width: 1200,\n            height: 630,\n            alt: title,\n          },\n        ],\n        locale: 'en_US',\n        type: ogType,\n        ...(publishedTime && { publishedTime }),\n        ...(modifiedTime && { modifiedTime }),\n        ...(section && { section }),\n        ...(tags.length > 0 && { tags }),\n      },\n      twitter: {\n        card: 'summary_large_image',\n        title: fullTitle,\n        description,\n        images: [ogImage || defaultImage],\n        creator: '@streamzen',\n        site: '@streamzen',\n      },\n      robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n          index: true,\n          follow: true,\n          'max-video-preview': -1,\n          'max-image-preview': 'large',\n          'max-snippet': -1,\n        },\n      },\n      verification: {\n        google: process.env.GOOGLE_VERIFICATION_ID,\n        yandex: process.env.YANDEX_VERIFICATION_ID,\n        yahoo: process.env.YAHOO_VERIFICATION_ID,\n      },\n    };\n  }\n\n  static generateMovieMetadata(movie: IMovie): Metadata {\n    const title = `Watch ${movie.title} (${movie.year}) Online Free`;\n    const description = `Watch ${movie.title} (${movie.year}) online free in HD quality. ${movie.description || `Starring ${movie.cast?.slice(0, 3).join(', ') || 'top actors'}. Stream now on ${this.siteName}.`}`;\n    \n    const keywords = [\n      movie.title,\n      `${movie.title} ${movie.year}`,\n      `watch ${movie.title}`,\n      `${movie.title} online`,\n      `${movie.title} free`,\n      'watch movies online',\n      'free movies',\n      'HD movies',\n      ...(movie.genres || []),\n      ...(movie.cast?.slice(0, 5) || []),\n      movie.director,\n      movie.language,\n      movie.country,\n    ].filter(Boolean);\n\n    return this.generateMetadata({\n      title,\n      description,\n      keywords,\n      canonical: `/watch/movie/${movie.imdbId}`,\n      ogImage: movie.posterUrl,\n      ogType: 'video.movie',\n      publishedTime: movie.createdAt ? new Date(movie.createdAt).toISOString() : undefined,\n      modifiedTime: movie.updatedAt ? new Date(movie.updatedAt).toISOString() : undefined,\n      authors: [movie.director].filter(Boolean),\n      section: 'Movies',\n      tags: movie.genres,\n    });\n  }\n\n  static generateSeriesMetadata(series: ISeries): Metadata {\n    const title = `Watch ${series.title} (${series.startYear}${series.endYear ? `-${series.endYear}` : ''}) Online Free`;\n    const description = `Watch ${series.title} TV series online free in HD quality. ${series.description || `${series.totalSeasons} seasons available. Starring ${series.cast?.slice(0, 3).join(', ') || 'top actors'}. Stream all episodes now on ${this.siteName}.`}`;\n    \n    const keywords = [\n      series.title,\n      `${series.title} ${series.startYear}`,\n      `watch ${series.title}`,\n      `${series.title} online`,\n      `${series.title} free`,\n      `${series.title} episodes`,\n      'watch series online',\n      'free TV shows',\n      'HD series',\n      ...(series.genres || []),\n      ...(series.cast?.slice(0, 5) || []),\n      series.language,\n      series.country,\n    ].filter(Boolean);\n\n    return this.generateMetadata({\n      title,\n      description,\n      keywords,\n      canonical: `/watch/series/${series.imdbId}`,\n      ogImage: series.posterUrl,\n      ogType: 'video.tv_show',\n      publishedTime: series.createdAt ? new Date(series.createdAt).toISOString() : undefined,\n      modifiedTime: series.updatedAt ? new Date(series.updatedAt).toISOString() : undefined,\n      section: 'TV Series',\n      tags: series.genres,\n    });\n  }\n\n  static generateEpisodeMetadata(episode: IEpisode, series?: ISeries): Metadata {\n    const episodeTitle = episode.episodeTitle || `Episode ${episode.episode}`;\n    const title = `Watch ${episode.seriesTitle} S${episode.season}E${episode.episode} - ${episodeTitle} Online Free`;\n    const description = `Watch ${episode.seriesTitle} Season ${episode.season} Episode ${episode.episode} \"${episodeTitle}\" online free in HD quality. ${episode.description || `Latest episode from ${episode.seriesTitle}. Stream now on ${this.siteName}.`}`;\n    \n    const keywords = [\n      episode.seriesTitle,\n      `${episode.seriesTitle} S${episode.season}E${episode.episode}`,\n      `${episode.seriesTitle} season ${episode.season}`,\n      `watch ${episode.seriesTitle}`,\n      episodeTitle,\n      'watch episodes online',\n      'free episodes',\n      'HD episodes',\n      ...(episode.genres || series?.genres || []),\n    ].filter(Boolean);\n\n    return this.generateMetadata({\n      title,\n      description,\n      keywords,\n      canonical: `/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,\n      ogImage: series?.posterUrl,\n      ogType: 'video.episode',\n      publishedTime: episode.createdAt ? new Date(episode.createdAt).toISOString() : undefined,\n      modifiedTime: episode.updatedAt ? new Date(episode.updatedAt).toISOString() : undefined,\n      section: 'Episodes',\n      tags: episode.genres || series?.genres,\n    });\n  }\n\n  static generatePageMetadata(\n    title: string,\n    description: string,\n    path: string,\n    additionalKeywords: string[] = []\n  ): Metadata {\n    const keywords = [\n      'watch movies online',\n      'free movies',\n      'HD movies',\n      'TV series online',\n      'free episodes',\n      'streaming platform',\n      this.siteName,\n      ...additionalKeywords,\n    ];\n\n    return this.generateMetadata({\n      title,\n      description,\n      keywords,\n      canonical: path,\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AAmBO,MAAM;IACX,OAAe,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI,wBAAwB;IACrF,OAAe,WAAW,YAAY;IACtC,OAAe,qBAAqB,gIAAgI;IAEpK,OAAO,iBAAiB,MAAiB,EAAY;QACnD,MAAM,EACJ,KAAK,EACL,WAAW,EACX,WAAW,EAAE,EACb,SAAS,EACT,OAAO,EACP,SAAS,SAAS,EAClB,aAAa,EACb,YAAY,EACZ,UAAU,EAAE,EACZ,OAAO,EACP,OAAO,EAAE,EACV,GAAG;QAEJ,MAAM,YAAY,MAAM,QAAQ,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE;QACvF,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,IAAI,CAAC,OAAO;QACpE,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;QAErD,OAAO;YACL,OAAO;YACP;YACA,UAAU,SAAS,IAAI,CAAC;YACxB,SAAS,QAAQ,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE;gBAAK,CAAC;YACtC,SAAS,IAAI,CAAC,QAAQ;YACtB,WAAW,IAAI,CAAC,QAAQ;YACxB,iBAAiB;gBACf,OAAO;gBACP,SAAS;gBACT,WAAW;YACb;YACA,cAAc,IAAI,IAAI,IAAI,CAAC,OAAO;YAClC,YAAY;gBACV,WAAW;YACb;YACA,WAAW;gBACT,OAAO;gBACP;gBACA;gBACA,UAAU,IAAI,CAAC,QAAQ;gBACvB,QAAQ;oBACN;wBACE,KAAK,WAAW;wBAChB,OAAO;wBACP,QAAQ;wBACR,KAAK;oBACP;iBACD;gBACD,QAAQ;gBACR,MAAM;gBACN,GAAI,iBAAiB;oBAAE;gBAAc,CAAC;gBACtC,GAAI,gBAAgB;oBAAE;gBAAa,CAAC;gBACpC,GAAI,WAAW;oBAAE;gBAAQ,CAAC;gBAC1B,GAAI,KAAK,MAAM,GAAG,KAAK;oBAAE;gBAAK,CAAC;YACjC;YACA,SAAS;gBACP,MAAM;gBACN,OAAO;gBACP;gBACA,QAAQ;oBAAC,WAAW;iBAAa;gBACjC,SAAS;gBACT,MAAM;YACR;YACA,QAAQ;gBACN,OAAO;gBACP,QAAQ;gBACR,WAAW;oBACT,OAAO;oBACP,QAAQ;oBACR,qBAAqB,CAAC;oBACtB,qBAAqB;oBACrB,eAAe,CAAC;gBAClB;YACF;YACA,cAAc;gBACZ,QAAQ,QAAQ,GAAG,CAAC,sBAAsB;gBAC1C,QAAQ,QAAQ,GAAG,CAAC,sBAAsB;gBAC1C,OAAO,QAAQ,GAAG,CAAC,qBAAqB;YAC1C;QACF;IACF;IAEA,OAAO,sBAAsB,KAAa,EAAY;QACpD,MAAM,QAAQ,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC;QAChE,MAAM,cAAc,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,6BAA6B,EAAE,MAAM,WAAW,IAAI,CAAC,SAAS,EAAE,MAAM,IAAI,EAAE,MAAM,GAAG,GAAG,KAAK,SAAS,aAAa,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QAE/M,MAAM,WAAW;YACf,MAAM,KAAK;YACX,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE;YAC9B,CAAC,MAAM,EAAE,MAAM,KAAK,EAAE;YACtB,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC;YACvB,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC;YACrB;YACA;YACA;eACI,MAAM,MAAM,IAAI,EAAE;eAClB,MAAM,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;YACjC,MAAM,QAAQ;YACd,MAAM,QAAQ;YACd,MAAM,OAAO;SACd,CAAC,MAAM,CAAC;QAET,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC3B;YACA;YACA;YACA,WAAW,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE;YACzC,SAAS,MAAM,SAAS;YACxB,QAAQ;YACR,eAAe,MAAM,SAAS,GAAG,IAAI,KAAK,MAAM,SAAS,EAAE,WAAW,KAAK;YAC3E,cAAc,MAAM,SAAS,GAAG,IAAI,KAAK,MAAM,SAAS,EAAE,WAAW,KAAK;YAC1E,SAAS;gBAAC,MAAM,QAAQ;aAAC,CAAC,MAAM,CAAC;YACjC,SAAS;YACT,MAAM,MAAM,MAAM;QACpB;IACF;IAEA,OAAO,uBAAuB,MAAe,EAAY;QACvD,MAAM,QAAQ,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,OAAO,SAAS,GAAG,OAAO,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,OAAO,EAAE,GAAG,GAAG,aAAa,CAAC;QACpH,MAAM,cAAc,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,sCAAsC,EAAE,OAAO,WAAW,IAAI,GAAG,OAAO,YAAY,CAAC,6BAA6B,EAAE,OAAO,IAAI,EAAE,MAAM,GAAG,GAAG,KAAK,SAAS,aAAa,6BAA6B,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QAEnQ,MAAM,WAAW;YACf,OAAO,KAAK;YACZ,GAAG,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,SAAS,EAAE;YACrC,CAAC,MAAM,EAAE,OAAO,KAAK,EAAE;YACvB,GAAG,OAAO,KAAK,CAAC,OAAO,CAAC;YACxB,GAAG,OAAO,KAAK,CAAC,KAAK,CAAC;YACtB,GAAG,OAAO,KAAK,CAAC,SAAS,CAAC;YAC1B;YACA;YACA;eACI,OAAO,MAAM,IAAI,EAAE;eACnB,OAAO,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;YAClC,OAAO,QAAQ;YACf,OAAO,OAAO;SACf,CAAC,MAAM,CAAC;QAET,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC3B;YACA;YACA;YACA,WAAW,CAAC,cAAc,EAAE,OAAO,MAAM,EAAE;YAC3C,SAAS,OAAO,SAAS;YACzB,QAAQ;YACR,eAAe,OAAO,SAAS,GAAG,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,KAAK;YAC7E,cAAc,OAAO,SAAS,GAAG,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,KAAK;YAC5E,SAAS;YACT,MAAM,OAAO,MAAM;QACrB;IACF;IAEA,OAAO,wBAAwB,OAAiB,EAAE,MAAgB,EAAY;QAC5E,MAAM,eAAe,QAAQ,YAAY,IAAI,CAAC,QAAQ,EAAE,QAAQ,OAAO,EAAE;QACzE,MAAM,QAAQ,CAAC,MAAM,EAAE,QAAQ,WAAW,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,GAAG,EAAE,aAAa,YAAY,CAAC;QAChH,MAAM,cAAc,CAAC,MAAM,EAAE,QAAQ,WAAW,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,CAAC,EAAE,EAAE,aAAa,6BAA6B,EAAE,QAAQ,WAAW,IAAI,CAAC,oBAAoB,EAAE,QAAQ,WAAW,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QAE3P,MAAM,WAAW;YACf,QAAQ,WAAW;YACnB,GAAG,QAAQ,WAAW,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,EAAE;YAC9D,GAAG,QAAQ,WAAW,CAAC,QAAQ,EAAE,QAAQ,MAAM,EAAE;YACjD,CAAC,MAAM,EAAE,QAAQ,WAAW,EAAE;YAC9B;YACA;YACA;YACA;eACI,QAAQ,MAAM,IAAI,QAAQ,UAAU,EAAE;SAC3C,CAAC,MAAM,CAAC;QAET,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC3B;YACA;YACA;YACA,WAAW,CAAC,cAAc,EAAE,QAAQ,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;YAChG,SAAS,QAAQ;YACjB,QAAQ;YACR,eAAe,QAAQ,SAAS,GAAG,IAAI,KAAK,QAAQ,SAAS,EAAE,WAAW,KAAK;YAC/E,cAAc,QAAQ,SAAS,GAAG,IAAI,KAAK,QAAQ,SAAS,EAAE,WAAW,KAAK;YAC9E,SAAS;YACT,MAAM,QAAQ,MAAM,IAAI,QAAQ;QAClC;IACF;IAEA,OAAO,qBACL,KAAa,EACb,WAAmB,EACnB,IAAY,EACZ,qBAA+B,EAAE,EACvB;QACV,MAAM,WAAW;YACf;YACA;YACA;YACA;YACA;YACA;YACA,IAAI,CAAC,QAAQ;eACV;SACJ;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC3B;YACA;YACA;YACA,WAAW;QACb;IACF;AACF", "debugId": null}}, {"offset": {"line": 478, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/schema.ts"], "sourcesContent": ["import { IMovie } from '@/models/Movie';\nimport { ISeries } from '@/models/Series';\nimport { IEpisode } from '@/models/Episode';\n\nexport interface SchemaMarkup {\n  '@context': string;\n  '@type': string;\n  [key: string]: any;\n}\n\nexport class SchemaGenerator {\n  private static baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://streamzen.com';\n  private static siteName = 'StreamZen';\n\n  static generateWebsiteSchema(): SchemaMarkup {\n    return {\n      '@context': 'https://schema.org',\n      '@type': 'WebSite',\n      name: this.siteName,\n      url: this.baseUrl,\n      description: 'Premium streaming platform with the latest movies, TV series, and episodes. Watch HD content with multiple streaming sources.',\n      potentialAction: {\n        '@type': 'SearchAction',\n        target: {\n          '@type': 'EntryPoint',\n          urlTemplate: `${this.baseUrl}/search?q={search_term_string}`,\n        },\n        'query-input': 'required name=search_term_string',\n      },\n      publisher: {\n        '@type': 'Organization',\n        name: this.siteName,\n        url: this.baseUrl,\n        logo: {\n          '@type': 'ImageObject',\n          url: `${this.baseUrl}/logo.png`,\n        },\n      },\n    };\n  }\n\n  static generateMovieSchema(movie: IMovie): SchemaMarkup {\n    const schema: SchemaMarkup = {\n      '@context': 'https://schema.org',\n      '@type': 'Movie',\n      name: movie.title,\n      url: `${this.baseUrl}/watch/movie/${movie.imdbId}`,\n      description: movie.description,\n      image: movie.posterUrl,\n      datePublished: movie.year?.toString(),\n      genre: movie.genres,\n      duration: movie.runtime,\n      contentRating: movie.rating,\n      aggregateRating: movie.imdbRating ? {\n        '@type': 'AggregateRating',\n        ratingValue: movie.imdbRating,\n        ratingCount: movie.imdbVotes?.replace(/,/g, '') || '1000',\n        bestRating: '10',\n        worstRating: '1',\n      } : undefined,\n      director: movie.director ? {\n        '@type': 'Person',\n        name: movie.director,\n      } : undefined,\n      actor: movie.cast?.slice(0, 5).map(actor => ({\n        '@type': 'Person',\n        name: actor,\n      })),\n      productionCompany: {\n        '@type': 'Organization',\n        name: movie.country || 'Unknown',\n      },\n      inLanguage: movie.language,\n      keywords: [\n        movie.title,\n        `${movie.title} ${movie.year}`,\n        'watch online',\n        'free movie',\n        'HD movie',\n        ...(movie.genres || []),\n      ].join(', '),\n      offers: {\n        '@type': 'Offer',\n        price: '0',\n        priceCurrency: 'USD',\n        availability: 'https://schema.org/InStock',\n        url: `${this.baseUrl}/watch/movie/${movie.imdbId}`,\n      },\n      potentialAction: {\n        '@type': 'WatchAction',\n        target: {\n          '@type': 'EntryPoint',\n          urlTemplate: `${this.baseUrl}/watch/movie/${movie.imdbId}`,\n          actionPlatform: [\n            'https://schema.org/DesktopWebPlatform',\n            'https://schema.org/MobileWebPlatform',\n          ],\n        },\n        expectsAcceptanceOf: {\n          '@type': 'Offer',\n          price: '0',\n          priceCurrency: 'USD',\n          eligibleRegion: {\n            '@type': 'Country',\n            name: 'US',\n          },\n        },\n      },\n    };\n\n    // Remove undefined values\n    return JSON.parse(JSON.stringify(schema));\n  }\n\n  static generateSeriesSchema(series: ISeries): SchemaMarkup {\n    const schema: SchemaMarkup = {\n      '@context': 'https://schema.org',\n      '@type': 'TVSeries',\n      name: series.title,\n      url: `${this.baseUrl}/watch/series/${series.imdbId}`,\n      description: series.description,\n      image: series.posterUrl,\n      startDate: series.startYear?.toString(),\n      endDate: series.endYear?.toString(),\n      numberOfSeasons: series.totalSeasons,\n      numberOfEpisodes: series.totalEpisodes,\n      genre: series.genres,\n      contentRating: series.rating,\n      aggregateRating: series.imdbRating ? {\n        '@type': 'AggregateRating',\n        ratingValue: series.imdbRating,\n        ratingCount: series.imdbVotes?.replace(/,/g, '') || '1000',\n        bestRating: '10',\n        worstRating: '1',\n      } : undefined,\n      actor: series.cast?.slice(0, 5).map(actor => ({\n        '@type': 'Person',\n        name: actor,\n      })),\n      productionCompany: {\n        '@type': 'Organization',\n        name: series.country || 'Unknown',\n      },\n      inLanguage: series.language,\n      keywords: [\n        series.title,\n        `${series.title} ${series.startYear}`,\n        'watch online',\n        'free series',\n        'HD series',\n        'TV show',\n        ...(series.genres || []),\n      ].join(', '),\n      offers: {\n        '@type': 'Offer',\n        price: '0',\n        priceCurrency: 'USD',\n        availability: 'https://schema.org/InStock',\n        url: `${this.baseUrl}/watch/series/${series.imdbId}`,\n      },\n      potentialAction: {\n        '@type': 'WatchAction',\n        target: {\n          '@type': 'EntryPoint',\n          urlTemplate: `${this.baseUrl}/watch/series/${series.imdbId}`,\n          actionPlatform: [\n            'https://schema.org/DesktopWebPlatform',\n            'https://schema.org/MobileWebPlatform',\n          ],\n        },\n        expectsAcceptanceOf: {\n          '@type': 'Offer',\n          price: '0',\n          priceCurrency: 'USD',\n          eligibleRegion: {\n            '@type': 'Country',\n            name: 'US',\n          },\n        },\n      },\n    };\n\n    return JSON.parse(JSON.stringify(schema));\n  }\n\n  static generateEpisodeSchema(episode: IEpisode, series?: ISeries): SchemaMarkup {\n    const episodeTitle = episode.episodeTitle || `Episode ${episode.episode}`;\n    \n    const schema: SchemaMarkup = {\n      '@context': 'https://schema.org',\n      '@type': 'TVEpisode',\n      name: episodeTitle,\n      episodeNumber: episode.episode,\n      seasonNumber: episode.season,\n      url: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,\n      description: episode.description || `${episode.seriesTitle} Season ${episode.season} Episode ${episode.episode}`,\n      image: series?.posterUrl,\n      datePublished: episode.airDate ? new Date(episode.airDate).toISOString() : undefined,\n      duration: episode.runtime,\n      partOfSeries: {\n        '@type': 'TVSeries',\n        name: episode.seriesTitle,\n        url: `${this.baseUrl}/watch/series/${episode.imdbId}`,\n      },\n      partOfSeason: {\n        '@type': 'TVSeason',\n        seasonNumber: episode.season,\n        partOfSeries: {\n          '@type': 'TVSeries',\n          name: episode.seriesTitle,\n        },\n      },\n      aggregateRating: episode.imdbRating ? {\n        '@type': 'AggregateRating',\n        ratingValue: episode.imdbRating,\n        bestRating: '10',\n        worstRating: '1',\n      } : undefined,\n      genre: episode.genres || series?.genres,\n      inLanguage: series?.language,\n      keywords: [\n        episode.seriesTitle,\n        episodeTitle,\n        `S${episode.season}E${episode.episode}`,\n        'watch online',\n        'free episode',\n        'HD episode',\n        ...(episode.genres || series?.genres || []),\n      ].join(', '),\n      offers: {\n        '@type': 'Offer',\n        price: '0',\n        priceCurrency: 'USD',\n        availability: 'https://schema.org/InStock',\n        url: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,\n      },\n      potentialAction: {\n        '@type': 'WatchAction',\n        target: {\n          '@type': 'EntryPoint',\n          urlTemplate: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,\n          actionPlatform: [\n            'https://schema.org/DesktopWebPlatform',\n            'https://schema.org/MobileWebPlatform',\n          ],\n        },\n      },\n    };\n\n    return JSON.parse(JSON.stringify(schema));\n  }\n\n  static generateBreadcrumbSchema(items: Array<{ name: string; url: string }>): SchemaMarkup {\n    return {\n      '@context': 'https://schema.org',\n      '@type': 'BreadcrumbList',\n      itemListElement: items.map((item, index) => ({\n        '@type': 'ListItem',\n        position: index + 1,\n        name: item.name,\n        item: `${this.baseUrl}${item.url}`,\n      })),\n    };\n  }\n\n  static generateCollectionPageSchema(\n    name: string,\n    description: string,\n    url: string,\n    items: Array<{ name: string; url: string; image?: string }>\n  ): SchemaMarkup {\n    return {\n      '@context': 'https://schema.org',\n      '@type': 'CollectionPage',\n      name,\n      description,\n      url: `${this.baseUrl}${url}`,\n      mainEntity: {\n        '@type': 'ItemList',\n        numberOfItems: items.length,\n        itemListElement: items.map((item, index) => ({\n          '@type': 'ListItem',\n          position: index + 1,\n          url: `${this.baseUrl}${item.url}`,\n          name: item.name,\n          ...(item.image && { image: item.image }),\n        })),\n      },\n    };\n  }\n}\n"], "names": [], "mappings": ";;;AAUO,MAAM;IACX,OAAe,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI,wBAAwB;IACrF,OAAe,WAAW,YAAY;IAEtC,OAAO,wBAAsC;QAC3C,OAAO;YACL,YAAY;YACZ,SAAS;YACT,MAAM,IAAI,CAAC,QAAQ;YACnB,KAAK,IAAI,CAAC,OAAO;YACjB,aAAa;YACb,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBACN,SAAS;oBACT,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC;gBAC9D;gBACA,eAAe;YACjB;YACA,WAAW;gBACT,SAAS;gBACT,MAAM,IAAI,CAAC,QAAQ;gBACnB,KAAK,IAAI,CAAC,OAAO;gBACjB,MAAM;oBACJ,SAAS;oBACT,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;gBACjC;YACF;QACF;IACF;IAEA,OAAO,oBAAoB,KAAa,EAAgB;QACtD,MAAM,SAAuB;YAC3B,YAAY;YACZ,SAAS;YACT,MAAM,MAAM,KAAK;YACjB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE;YAClD,aAAa,MAAM,WAAW;YAC9B,OAAO,MAAM,SAAS;YACtB,eAAe,MAAM,IAAI,EAAE;YAC3B,OAAO,MAAM,MAAM;YACnB,UAAU,MAAM,OAAO;YACvB,eAAe,MAAM,MAAM;YAC3B,iBAAiB,MAAM,UAAU,GAAG;gBAClC,SAAS;gBACT,aAAa,MAAM,UAAU;gBAC7B,aAAa,MAAM,SAAS,EAAE,QAAQ,MAAM,OAAO;gBACnD,YAAY;gBACZ,aAAa;YACf,IAAI;YACJ,UAAU,MAAM,QAAQ,GAAG;gBACzB,SAAS;gBACT,MAAM,MAAM,QAAQ;YACtB,IAAI;YACJ,OAAO,MAAM,IAAI,EAAE,MAAM,GAAG,GAAG,IAAI,CAAA,QAAS,CAAC;oBAC3C,SAAS;oBACT,MAAM;gBACR,CAAC;YACD,mBAAmB;gBACjB,SAAS;gBACT,MAAM,MAAM,OAAO,IAAI;YACzB;YACA,YAAY,MAAM,QAAQ;YAC1B,UAAU;gBACR,MAAM,KAAK;gBACX,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE;gBAC9B;gBACA;gBACA;mBACI,MAAM,MAAM,IAAI,EAAE;aACvB,CAAC,IAAI,CAAC;YACP,QAAQ;gBACN,SAAS;gBACT,OAAO;gBACP,eAAe;gBACf,cAAc;gBACd,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE;YACpD;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBACN,SAAS;oBACT,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE;oBAC1D,gBAAgB;wBACd;wBACA;qBACD;gBACH;gBACA,qBAAqB;oBACnB,SAAS;oBACT,OAAO;oBACP,eAAe;oBACf,gBAAgB;wBACd,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;QACF;QAEA,0BAA0B;QAC1B,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;IACnC;IAEA,OAAO,qBAAqB,MAAe,EAAgB;QACzD,MAAM,SAAuB;YAC3B,YAAY;YACZ,SAAS;YACT,MAAM,OAAO,KAAK;YAClB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,MAAM,EAAE;YACpD,aAAa,OAAO,WAAW;YAC/B,OAAO,OAAO,SAAS;YACvB,WAAW,OAAO,SAAS,EAAE;YAC7B,SAAS,OAAO,OAAO,EAAE;YACzB,iBAAiB,OAAO,YAAY;YACpC,kBAAkB,OAAO,aAAa;YACtC,OAAO,OAAO,MAAM;YACpB,eAAe,OAAO,MAAM;YAC5B,iBAAiB,OAAO,UAAU,GAAG;gBACnC,SAAS;gBACT,aAAa,OAAO,UAAU;gBAC9B,aAAa,OAAO,SAAS,EAAE,QAAQ,MAAM,OAAO;gBACpD,YAAY;gBACZ,aAAa;YACf,IAAI;YACJ,OAAO,OAAO,IAAI,EAAE,MAAM,GAAG,GAAG,IAAI,CAAA,QAAS,CAAC;oBAC5C,SAAS;oBACT,MAAM;gBACR,CAAC;YACD,mBAAmB;gBACjB,SAAS;gBACT,MAAM,OAAO,OAAO,IAAI;YAC1B;YACA,YAAY,OAAO,QAAQ;YAC3B,UAAU;gBACR,OAAO,KAAK;gBACZ,GAAG,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,SAAS,EAAE;gBACrC;gBACA;gBACA;gBACA;mBACI,OAAO,MAAM,IAAI,EAAE;aACxB,CAAC,IAAI,CAAC;YACP,QAAQ;gBACN,SAAS;gBACT,OAAO;gBACP,eAAe;gBACf,cAAc;gBACd,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,MAAM,EAAE;YACtD;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBACN,SAAS;oBACT,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,MAAM,EAAE;oBAC5D,gBAAgB;wBACd;wBACA;qBACD;gBACH;gBACA,qBAAqB;oBACnB,SAAS;oBACT,OAAO;oBACP,eAAe;oBACf,gBAAgB;wBACd,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;QACF;QAEA,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;IACnC;IAEA,OAAO,sBAAsB,OAAiB,EAAE,MAAgB,EAAgB;QAC9E,MAAM,eAAe,QAAQ,YAAY,IAAI,CAAC,QAAQ,EAAE,QAAQ,OAAO,EAAE;QAEzE,MAAM,SAAuB;YAC3B,YAAY;YACZ,SAAS;YACT,MAAM;YACN,eAAe,QAAQ,OAAO;YAC9B,cAAc,QAAQ,MAAM;YAC5B,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,QAAQ,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;YACzG,aAAa,QAAQ,WAAW,IAAI,GAAG,QAAQ,WAAW,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;YAChH,OAAO,QAAQ;YACf,eAAe,QAAQ,OAAO,GAAG,IAAI,KAAK,QAAQ,OAAO,EAAE,WAAW,KAAK;YAC3E,UAAU,QAAQ,OAAO;YACzB,cAAc;gBACZ,SAAS;gBACT,MAAM,QAAQ,WAAW;gBACzB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,QAAQ,MAAM,EAAE;YACvD;YACA,cAAc;gBACZ,SAAS;gBACT,cAAc,QAAQ,MAAM;gBAC5B,cAAc;oBACZ,SAAS;oBACT,MAAM,QAAQ,WAAW;gBAC3B;YACF;YACA,iBAAiB,QAAQ,UAAU,GAAG;gBACpC,SAAS;gBACT,aAAa,QAAQ,UAAU;gBAC/B,YAAY;gBACZ,aAAa;YACf,IAAI;YACJ,OAAO,QAAQ,MAAM,IAAI,QAAQ;YACjC,YAAY,QAAQ;YACpB,UAAU;gBACR,QAAQ,WAAW;gBACnB;gBACA,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,EAAE;gBACvC;gBACA;gBACA;mBACI,QAAQ,MAAM,IAAI,QAAQ,UAAU,EAAE;aAC3C,CAAC,IAAI,CAAC;YACP,QAAQ;gBACN,SAAS;gBACT,OAAO;gBACP,eAAe;gBACf,cAAc;gBACd,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,QAAQ,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;YAC3G;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBACN,SAAS;oBACT,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,QAAQ,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;oBACjH,gBAAgB;wBACd;wBACA;qBACD;gBACH;YACF;QACF;QAEA,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;IACnC;IAEA,OAAO,yBAAyB,KAA2C,EAAgB;QACzF,OAAO;YACL,YAAY;YACZ,SAAS;YACT,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;oBAC3C,SAAS;oBACT,UAAU,QAAQ;oBAClB,MAAM,KAAK,IAAI;oBACf,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,EAAE;gBACpC,CAAC;QACH;IACF;IAEA,OAAO,6BACL,IAAY,EACZ,WAAmB,EACnB,GAAW,EACX,KAA2D,EAC7C;QACd,OAAO;YACL,YAAY;YACZ,SAAS;YACT;YACA;YACA,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK;YAC5B,YAAY;gBACV,SAAS;gBACT,eAAe,MAAM,MAAM;gBAC3B,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;wBAC3C,SAAS;wBACT,UAAU,QAAQ;wBAClB,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,EAAE;wBACjC,MAAM,KAAK,IAAI;wBACf,GAAI,KAAK,KAAK,IAAI;4BAAE,OAAO,KAAK,KAAK;wBAAC,CAAC;oBACzC,CAAC;YACH;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/movies/page.tsx"], "sourcesContent": ["import { Suspense } from 'react';\nimport { Metadata } from 'next';\nimport { apiClient } from '@/lib/api';\nimport ContentGrid from '@/components/ContentGrid';\nimport FilterSidebar from '@/components/FilterSidebar';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ClientSearchBar from '@/components/ClientSearchBar';\nimport { SEOGenerator } from '@/lib/seo';\nimport { SchemaGenerator } from '@/lib/schema';\n\ninterface MoviesPageProps {\n  searchParams: Promise<{\n    page?: string;\n    genre?: string;\n    year?: string;\n    language?: string;\n    country?: string;\n    rating?: string;\n    quality?: string;\n    sortBy?: string;\n    sortOrder?: string;\n    search?: string;\n  }>;\n}\n\nexport async function generateMetadata({ searchParams }: MoviesPageProps): Promise<Metadata> {\n  const resolvedSearchParams = await searchParams;\n  const { genre, year, search, page } = resolvedSearchParams;\n\n  let title = 'Watch Movies Online Free';\n  let description = 'Watch the latest movies online free in HD quality. Discover thousands of movies from all genres including action, comedy, drama, horror, thriller, romance and more.';\n  let keywords = ['watch movies online', 'free movies', 'HD movies', 'latest movies', 'movie streaming'];\n\n  // Dynamic title and description based on filters\n  if (search) {\n    title = `Search Results for \"${search}\" - Movies`;\n    description = `Search results for \"${search}\" movies. Watch ${search} movies online free in HD quality on StreamZen.`;\n    keywords.push(search, `${search} movies`, `watch ${search}`);\n  } else if (genre && year) {\n    title = `${genre} Movies ${year} - Watch Online Free`;\n    description = `Watch the best ${genre} movies from ${year} online free in HD quality. Discover top-rated ${genre} films from ${year}.`;\n    keywords.push(genre, `${genre} movies`, `${year} movies`, `${genre} ${year}`);\n  } else if (genre) {\n    title = `${genre} Movies - Watch Online Free`;\n    description = `Watch the best ${genre} movies online free in HD quality. Discover top-rated ${genre} films and blockbusters.`;\n    keywords.push(genre, `${genre} movies`, `best ${genre} movies`);\n  } else if (year) {\n    title = `${year} Movies - Watch Online Free`;\n    description = `Watch the best movies from ${year} online free in HD quality. Discover top-rated films and blockbusters from ${year}.`;\n    keywords.push(`${year} movies`, `movies ${year}`, `best ${year} movies`);\n  }\n\n  if (page && parseInt(page) > 1) {\n    title += ` - Page ${page}`;\n    description += ` Browse page ${page} for more movies.`;\n  }\n\n  const path = `/movies${Object.keys(resolvedSearchParams).length > 0 ? '?' + new URLSearchParams(resolvedSearchParams as any).toString() : ''}`;\n\n  return SEOGenerator.generatePageMetadata(title, description, path, keywords);\n}\n\nasync function getMovies(searchParams: Awaited<MoviesPageProps['searchParams']>) {\n  try {\n    const filters = {\n      page: searchParams.page ? parseInt(searchParams.page) : 1,\n      limit: 24,\n      genre: searchParams.genre,\n      year: searchParams.year ? parseInt(searchParams.year) : undefined,\n      language: searchParams.language,\n      country: searchParams.country,\n      rating: searchParams.rating,\n      quality: searchParams.quality,\n      sortBy: (searchParams.sortBy as any) || 'createdAt',\n      sortOrder: (searchParams.sortOrder as 'asc' | 'desc') || 'desc',\n      search: searchParams.search,\n    };\n\n    const response = await apiClient.getMovies(filters);\n    return response;\n  } catch (error) {\n    console.error('Error fetching movies:', error);\n    return {\n      data: [],\n      pagination: {\n        page: 1,\n        limit: 24,\n        total: 0,\n        pages: 0\n      }\n    };\n  }\n}\n\nfunction transformMovieToContentItem(movie: any) {\n  return {\n    id: movie._id,\n    imdbId: movie.imdbId,\n    title: movie.title,\n    year: movie.year,\n    posterUrl: movie.posterUrl,\n    imdbRating: movie.imdbRating,\n    description: movie.description,\n    type: 'movie' as const\n  };\n}\n\nexport default async function MoviesPage({ searchParams }: MoviesPageProps) {\n  const resolvedSearchParams = await searchParams;\n  const { data: movies, pagination } = await getMovies(resolvedSearchParams);\n\n  // Generate structured data for SEO\n  const collectionSchema = SchemaGenerator.generateCollectionPageSchema(\n    resolvedSearchParams.search ? `Search Results for \"${resolvedSearchParams.search}\"` : 'Movies',\n    'Watch the latest movies online free in HD quality. Discover thousands of movies from all genres.',\n    `/movies${Object.keys(resolvedSearchParams).length > 0 ? '?' + new URLSearchParams(resolvedSearchParams as any).toString() : ''}`,\n    movies.slice(0, 10).map(movie => ({\n      name: `${movie.title} (${movie.year})`,\n      url: `/watch/movie/${movie.imdbId}`,\n      image: movie.posterUrl\n    }))\n  );\n\n  const breadcrumbSchema = SchemaGenerator.generateBreadcrumbSchema([\n    { name: 'Home', url: '/' },\n    { name: 'Movies', url: '/movies' },\n    ...(resolvedSearchParams.search ? [{ name: `Search: ${resolvedSearchParams.search}`, url: `/movies?search=${resolvedSearchParams.search}` }] : []),\n    ...(resolvedSearchParams.genre ? [{ name: resolvedSearchParams.genre, url: `/movies?genre=${resolvedSearchParams.genre}` }] : [])\n  ]);\n\n  return (\n    <div className=\"min-h-screen bg-black\">\n      {/* Structured Data */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify([\n            SchemaGenerator.generateWebsiteSchema(),\n            collectionSchema,\n            breadcrumbSchema\n          ])\n        }}\n      />\n      {/* Enhanced Hero Header */}\n      <div className=\"relative bg-black border-b border-gray-800/50 overflow-hidden\">\n        {/* Premium Background Effects */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-0 left-1/4 w-96 h-96 bg-gray-800/20 rounded-full blur-3xl animate-pulse\" />\n          <div className=\"absolute top-1/2 right-1/4 w-80 h-80 bg-gray-700/15 rounded-full blur-3xl animate-pulse\" style={{ animationDelay: '2s' }} />\n          <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-[600px] h-32 bg-gradient-to-t from-gray-900/30 to-transparent blur-xl\" />\n        </div>\n\n        <div className=\"relative max-w-[2560px] mx-auto px-6 lg:px-12 py-16 lg:py-24\">\n          <div className=\"mb-12\">\n            {/* Premium Title with Gradient Text */}\n            <div className=\"mb-6\">\n              <h1 className=\"text-5xl lg:text-7xl xl:text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-gray-200 to-gray-400 mb-6 tracking-tight leading-none\">\n                Movies\n              </h1>\n              <div className=\"w-24 h-1 bg-gradient-to-r from-red-500 to-gray-600 rounded-full mb-8\"></div>\n            </div>\n\n            <p className=\"text-xl lg:text-2xl text-gray-300 max-w-3xl leading-relaxed font-light mb-8\">\n              Discover premium movies in stunning quality. Experience cinema like never before with our curated collection of blockbusters, indie gems, and timeless classics.\n            </p>\n\n\n          </div>\n\n          {/* Enhanced Stats Cards */}\n          <div className=\"flex flex-wrap items-center gap-6\">\n            <div className=\"glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-3 h-3 bg-red-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-red-400 font-bold text-lg\">{pagination.total.toLocaleString()}</span>\n                <span className=\"text-gray-400 text-lg\">Movies Available</span>\n              </div>\n            </div>\n            <div className=\"glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\" style={{ animationDelay: '1s' }}></div>\n                <span className=\"text-green-400 font-bold text-lg\">4K Ultra HD</span>\n                <span className=\"text-gray-400 text-lg\">Premium Quality</span>\n              </div>\n            </div>\n            <div className=\"glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-3 h-3 bg-blue-500 rounded-full animate-pulse\" style={{ animationDelay: '2s' }}></div>\n                <span className=\"text-blue-400 font-bold text-lg\">Daily Updates</span>\n                <span className=\"text-gray-400 text-lg\">Fresh Content</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Enhanced Main Content */}\n      <div className=\"relative\">\n        {/* Subtle Background Effects */}\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n          <div className=\"absolute top-1/3 right-1/4 w-72 h-72 bg-gray-800/5 rounded-full blur-3xl\" />\n          <div className=\"absolute bottom-1/3 left-1/4 w-64 h-64 bg-gray-700/5 rounded-full blur-3xl\" />\n        </div>\n\n        <div className=\"relative max-w-[2560px] mx-auto px-6 lg:px-12 py-12\">\n          <div className=\"flex gap-12\">\n            {/* Enhanced Sidebar */}\n            <div className=\"w-80 flex-shrink-0\">\n              <div className=\"sticky top-8\">\n                <FilterSidebar\n                  currentFilters={resolvedSearchParams}\n                  basePath=\"/movies\"\n                  contentType=\"movies\"\n                />\n              </div>\n            </div>\n\n            {/* Enhanced Content Grid */}\n            <div className=\"flex-1\">\n              <div className=\"mb-8\">\n                <div className=\"flex items-center justify-between mb-8\">\n                  <h2 className=\"text-3xl font-bold text-white tracking-tight\">\n                    {resolvedSearchParams.search ? `Search Results for \"${resolvedSearchParams.search}\"` : 'All Movies'}\n                  </h2>\n                  <div className=\"glass-elevated px-4 py-2 rounded-xl border border-gray-700/50\">\n                    <span className=\"text-gray-300 text-base font-medium\">\n                      Page {pagination.page} of {pagination.pages}\n                    </span>\n                  </div>\n                </div>\n\n                <Suspense fallback={<LoadingSpinner />}>\n                  <ContentGrid\n                    items={movies.map(transformMovieToContentItem)}\n                    pagination={pagination}\n                    basePath=\"/movies\"\n                    currentFilters={resolvedSearchParams}\n                  />\n                </Suspense>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;AAiBO,eAAe,iBAAiB,EAAE,YAAY,EAAmB;IACtE,MAAM,uBAAuB,MAAM;IACnC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;IAEtC,IAAI,QAAQ;IACZ,IAAI,cAAc;IAClB,IAAI,WAAW;QAAC;QAAuB;QAAe;QAAa;QAAiB;KAAkB;IAEtG,iDAAiD;IACjD,IAAI,QAAQ;QACV,QAAQ,CAAC,oBAAoB,EAAE,OAAO,UAAU,CAAC;QACjD,cAAc,CAAC,oBAAoB,EAAE,OAAO,gBAAgB,EAAE,OAAO,+CAA+C,CAAC;QACrH,SAAS,IAAI,CAAC,QAAQ,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ;IAC7D,OAAO,IAAI,SAAS,MAAM;QACxB,QAAQ,GAAG,MAAM,QAAQ,EAAE,KAAK,oBAAoB,CAAC;QACrD,cAAc,CAAC,eAAe,EAAE,MAAM,aAAa,EAAE,KAAK,+CAA+C,EAAE,MAAM,YAAY,EAAE,KAAK,CAAC,CAAC;QACtI,SAAS,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,EAAE,GAAG,KAAK,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,MAAM;IAC9E,OAAO,IAAI,OAAO;QAChB,QAAQ,GAAG,MAAM,2BAA2B,CAAC;QAC7C,cAAc,CAAC,eAAe,EAAE,MAAM,sDAAsD,EAAE,MAAM,wBAAwB,CAAC;QAC7H,SAAS,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,OAAO,CAAC;IAChE,OAAO,IAAI,MAAM;QACf,QAAQ,GAAG,KAAK,2BAA2B,CAAC;QAC5C,cAAc,CAAC,2BAA2B,EAAE,KAAK,2EAA2E,EAAE,KAAK,CAAC,CAAC;QACrI,SAAS,IAAI,CAAC,GAAG,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC;IACzE;IAEA,IAAI,QAAQ,SAAS,QAAQ,GAAG;QAC9B,SAAS,CAAC,QAAQ,EAAE,MAAM;QAC1B,eAAe,CAAC,aAAa,EAAE,KAAK,iBAAiB,CAAC;IACxD;IAEA,MAAM,OAAO,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,sBAAsB,MAAM,GAAG,IAAI,MAAM,IAAI,gBAAgB,sBAA6B,QAAQ,KAAK,IAAI;IAE9I,OAAO,iHAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC,OAAO,aAAa,MAAM;AACrE;AAEA,eAAe,UAAU,YAAsD;IAC7E,IAAI;QACF,MAAM,UAAU;YACd,MAAM,aAAa,IAAI,GAAG,SAAS,aAAa,IAAI,IAAI;YACxD,OAAO;YACP,OAAO,aAAa,KAAK;YACzB,MAAM,aAAa,IAAI,GAAG,SAAS,aAAa,IAAI,IAAI;YACxD,UAAU,aAAa,QAAQ;YAC/B,SAAS,aAAa,OAAO;YAC7B,QAAQ,aAAa,MAAM;YAC3B,SAAS,aAAa,OAAO;YAC7B,QAAQ,AAAC,aAAa,MAAM,IAAY;YACxC,WAAW,AAAC,aAAa,SAAS,IAAuB;YACzD,QAAQ,aAAa,MAAM;QAC7B;QAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;QAC3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YACL,MAAM,EAAE;YACR,YAAY;gBACV,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,OAAO;YACT;QACF;IACF;AACF;AAEA,SAAS,4BAA4B,KAAU;IAC7C,OAAO;QACL,IAAI,MAAM,GAAG;QACb,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,MAAM,MAAM,IAAI;QAChB,WAAW,MAAM,SAAS;QAC1B,YAAY,MAAM,UAAU;QAC5B,aAAa,MAAM,WAAW;QAC9B,MAAM;IACR;AACF;AAEe,eAAe,WAAW,EAAE,YAAY,EAAmB;IACxE,MAAM,uBAAuB,MAAM;IACnC,MAAM,EAAE,MAAM,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,UAAU;IAErD,mCAAmC;IACnC,MAAM,mBAAmB,oHAAA,CAAA,kBAAe,CAAC,4BAA4B,CACnE,qBAAqB,MAAM,GAAG,CAAC,oBAAoB,EAAE,qBAAqB,MAAM,CAAC,CAAC,CAAC,GAAG,UACtF,oGACA,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,sBAAsB,MAAM,GAAG,IAAI,MAAM,IAAI,gBAAgB,sBAA6B,QAAQ,KAAK,IAAI,EACjI,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,QAAS,CAAC;YAChC,MAAM,GAAG,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;YACtC,KAAK,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE;YACnC,OAAO,MAAM,SAAS;QACxB,CAAC;IAGH,MAAM,mBAAmB,oHAAA,CAAA,kBAAe,CAAC,wBAAwB,CAAC;QAChE;YAAE,MAAM;YAAQ,KAAK;QAAI;QACzB;YAAE,MAAM;YAAU,KAAK;QAAU;WAC7B,qBAAqB,MAAM,GAAG;YAAC;gBAAE,MAAM,CAAC,QAAQ,EAAE,qBAAqB,MAAM,EAAE;gBAAE,KAAK,CAAC,eAAe,EAAE,qBAAqB,MAAM,EAAE;YAAC;SAAE,GAAG,EAAE;WAC7I,qBAAqB,KAAK,GAAG;YAAC;gBAAE,MAAM,qBAAqB,KAAK;gBAAE,KAAK,CAAC,cAAc,EAAE,qBAAqB,KAAK,EAAE;YAAC;SAAE,GAAG,EAAE;KACjI;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;wBACrB,oHAAA,CAAA,kBAAe,CAAC,qBAAqB;wBACrC;wBACA;qBACD;gBACH;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;gCAA0F,OAAO;oCAAE,gBAAgB;gCAAK;;;;;;0CACvI,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAkK;;;;;;0DAGhL,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAGjB,8OAAC;wCAAE,WAAU;kDAA8E;;;;;;;;;;;;0CAQ7F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAkC,WAAW,KAAK,CAAC,cAAc;;;;;;8DACjF,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAkD,OAAO;wDAAE,gBAAgB;oDAAK;;;;;;8DAC/F,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAiD,OAAO;wDAAE,gBAAgB;oDAAK;;;;;;8DAC9F,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;8DAClD,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,mIAAA,CAAA,UAAa;4CACZ,gBAAgB;4CAChB,UAAS;4CACT,aAAY;;;;;;;;;;;;;;;;8CAMlB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,qBAAqB,MAAM,GAAG,CAAC,oBAAoB,EAAE,qBAAqB,MAAM,CAAC,CAAC,CAAC,GAAG;;;;;;kEAEzF,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;;gEAAsC;gEAC9C,WAAW,IAAI;gEAAC;gEAAK,WAAW,KAAK;;;;;;;;;;;;;;;;;;0DAKjD,8OAAC,qMAAA,CAAA,WAAQ;gDAAC,wBAAU,8OAAC,oIAAA,CAAA,UAAc;;;;;0DACjC,cAAA,8OAAC,iIAAA,CAAA,UAAW;oDACV,OAAO,OAAO,GAAG,CAAC;oDAClB,YAAY;oDACZ,UAAS;oDACT,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpC", "debugId": null}}, {"offset": {"line": 1313, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1376, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,+MAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,uKAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1416, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,yMACjF,gBAAA,yJAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,+KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,+KAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1448, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,6BAA+B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1524, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,KAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}